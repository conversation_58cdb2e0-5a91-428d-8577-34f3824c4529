# -*- coding: utf-8 -*-

# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/iam_policy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import field_behavior_pb2 as google_dot_api_dot_field__behavior__pb2
from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.api import client_pb2 as google_dot_api_dot_client__pb2
from google.api import resource_pb2 as google_dot_api_dot_resource__pb2
from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2

from google.iam.v1 import options_pb2 as google_dot_iam_dot_v1_dot_options__pb2
from google.iam.v1 import policy_pb2 as google_dot_iam_dot_v1_dot_policy__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x1egoogle/iam/v1/iam_policy.proto\x12\rgoogle.iam.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/resource.proto\x1a\x1bgoogle/iam/v1/options.proto\x1a\x1agoogle/iam/v1/policy.proto\x1a google/protobuf/field_mask.proto"\x8f\x01\n\x13SetIamPolicyRequest\x12\x1b\n\x08resource\x18\x01 \x01(\tB\t\xe0\x41\x02\xfa\x41\x03\n\x01*\x12*\n\x06policy\x18\x02 \x01(\x0b\x32\x15.google.iam.v1.PolicyB\x03\xe0\x41\x02\x12/\n\x0bupdate_mask\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.FieldMask"d\n\x13GetIamPolicyRequest\x12\x1b\n\x08resource\x18\x01 \x01(\tB\t\xe0\x41\x02\xfa\x41\x03\n\x01*\x12\x30\n\x07options\x18\x02 \x01(\x0b\x32\x1f.google.iam.v1.GetPolicyOptions"R\n\x19TestIamPermissionsRequest\x12\x1b\n\x08resource\x18\x01 \x01(\tB\t\xe0\x41\x02\xfa\x41\x03\n\x01*\x12\x18\n\x0bpermissions\x18\x02 \x03(\tB\x03\xe0\x41\x02"1\n\x1aTestIamPermissionsResponse\x12\x13\n\x0bpermissions\x18\x01 \x03(\t2\xb4\x03\n\tIAMPolicy\x12t\n\x0cSetIamPolicy\x12".google.iam.v1.SetIamPolicyRequest\x1a\x15.google.iam.v1.Policy")\x82\xd3\xe4\x93\x02#"\x1e/v1/{resource=**}:setIamPolicy:\x01*\x12t\n\x0cGetIamPolicy\x12".google.iam.v1.GetIamPolicyRequest\x1a\x15.google.iam.v1.Policy")\x82\xd3\xe4\x93\x02#"\x1e/v1/{resource=**}:getIamPolicy:\x01*\x12\x9a\x01\n\x12TestIamPermissions\x12(.google.iam.v1.TestIamPermissionsRequest\x1a).google.iam.v1.TestIamPermissionsResponse"/\x82\xd3\xe4\x93\x02)"$/v1/{resource=**}:testIamPermissions:\x01*\x1a\x1e\xca\x41\x1biam-meta-api.googleapis.comB|\n\x11\x63om.google.iam.v1B\x0eIamPolicyProtoP\x01Z)cloud.google.com/go/iam/apiv1/iampb;iampb\xaa\x02\x13Google.Cloud.Iam.V1\xca\x02\x13Google\\Cloud\\Iam\\V1b\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "google.iam.v1.iam_policy_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\021com.google.iam.v1B\016IamPolicyProtoP\001Z)cloud.google.com/go/iam/apiv1/iampb;iampb\252\002\023Google.Cloud.Iam.V1\312\002\023Google\\Cloud\\Iam\\V1"
    _SETIAMPOLICYREQUEST.fields_by_name["resource"]._options = None
    _SETIAMPOLICYREQUEST.fields_by_name[
        "resource"
    ]._serialized_options = b"\340A\002\372A\003\n\001*"
    _SETIAMPOLICYREQUEST.fields_by_name["policy"]._options = None
    _SETIAMPOLICYREQUEST.fields_by_name["policy"]._serialized_options = b"\340A\002"
    _GETIAMPOLICYREQUEST.fields_by_name["resource"]._options = None
    _GETIAMPOLICYREQUEST.fields_by_name[
        "resource"
    ]._serialized_options = b"\340A\002\372A\003\n\001*"
    _TESTIAMPERMISSIONSREQUEST.fields_by_name["resource"]._options = None
    _TESTIAMPERMISSIONSREQUEST.fields_by_name[
        "resource"
    ]._serialized_options = b"\340A\002\372A\003\n\001*"
    _TESTIAMPERMISSIONSREQUEST.fields_by_name["permissions"]._options = None
    _TESTIAMPERMISSIONSREQUEST.fields_by_name[
        "permissions"
    ]._serialized_options = b"\340A\002"
    _IAMPOLICY._options = None
    _IAMPOLICY._serialized_options = b"\312A\033iam-meta-api.googleapis.com"
    _IAMPOLICY.methods_by_name["SetIamPolicy"]._options = None
    _IAMPOLICY.methods_by_name[
        "SetIamPolicy"
    ]._serialized_options = (
        b'\202\323\344\223\002#"\036/v1/{resource=**}:setIamPolicy:\001*'
    )
    _IAMPOLICY.methods_by_name["GetIamPolicy"]._options = None
    _IAMPOLICY.methods_by_name[
        "GetIamPolicy"
    ]._serialized_options = (
        b'\202\323\344\223\002#"\036/v1/{resource=**}:getIamPolicy:\001*'
    )
    _IAMPOLICY.methods_by_name["TestIamPermissions"]._options = None
    _IAMPOLICY.methods_by_name[
        "TestIamPermissions"
    ]._serialized_options = (
        b'\202\323\344\223\002)"$/v1/{resource=**}:testIamPermissions:\001*'
    )
    _globals["_SETIAMPOLICYREQUEST"]._serialized_start = 256
    _globals["_SETIAMPOLICYREQUEST"]._serialized_end = 399
    _globals["_GETIAMPOLICYREQUEST"]._serialized_start = 401
    _globals["_GETIAMPOLICYREQUEST"]._serialized_end = 501
    _globals["_TESTIAMPERMISSIONSREQUEST"]._serialized_start = 503
    _globals["_TESTIAMPERMISSIONSREQUEST"]._serialized_end = 585
    _globals["_TESTIAMPERMISSIONSRESPONSE"]._serialized_start = 587
    _globals["_TESTIAMPERMISSIONSRESPONSE"]._serialized_end = 636
    _globals["_IAMPOLICY"]._serialized_start = 639
    _globals["_IAMPOLICY"]._serialized_end = 1075
# @@protoc_insertion_point(module_scope)
