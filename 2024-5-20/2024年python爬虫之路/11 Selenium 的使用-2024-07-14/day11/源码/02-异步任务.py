import hashlib
import time
import asyncio
import aiomysql
import aiohttp
import redis
from lxml import etree


class QiChe():
    def __init__(self):
        self.url = 'https://chejiahao.autohome.com.cn/Authors/AuthorListMore?orderType=3&page={}&userCategory=13'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36'
        }
        self.red = redis.Redis()

    async def get_data(self, page, client, conn):
        response = await client.get(self.url.format(page))
        # response.encoding()
        data = await response.text(encoding='utf-8')
        # print(data)
        html = etree.HTML(data)
        div_list = html.xpath('//div[@class="list-box"]')
        # print(div_list)
        # tasks = []
        for div in div_list:
            item = {}
            item['author'] = div.xpath('./a/div/div[2]/div[1]/text()')[0]
            item['intro'] = div.xpath('./a/div/div[2]/div[2]/text()')[0]
            item['fans'] = div.xpath('./a/div/div[3]/span[1]/text()')[0]
            item['works'] = div.xpath('./a/div/div[3]/span[3]/text()')[0]
            await self.save_data(item, conn)

    def get_md5(self, val):
        """把目标数据进行哈希，用哈希值去重更快"""
        md5 = hashlib.md5()
        md5.update(str(val).encode('utf-8'))
        # print(md5.hexdigest())
        return md5.hexdigest()


    async def save_data(self, item, pool):
        # print(conn.)
        # 连接mysql
        async with pool.acquire() as conn:
            # 创建游标
            async with conn.cursor() as cursor:
                val = self.get_md5(item)
                res = self.red.sadd('qic',  val)
                if res:
                    # sql插入语法
                    sql = 'INSERT INTO qiche(id, author, intro, fans, works) values(%s, %s, %s, %s, %s)'
                    try:
                        # print(sql, (0, item['authors'], item['title'], item['score']))
                        await cursor.execute(sql, (
                            0, item['author'], item['intro'], item['fans'], item['works']))
                        # 提交到数据库执行
                        await conn.commit()
                        print('数据插入成功...')
                    except Exception as e:
                        print(f'数据插入失败: {e}')
                        # 如果发生错误就回滚
                        await conn.rollback()

        # 异步创建连接池
        # conn = await aiomysql.connect(host='127.0.0.1', port=3306, user='root', password='root', db='spiders16',
        #                               loop=loop)
        # # 链接池创建链接的方式
        # # acq = await conn.acquire()
        # cursor = await conn.cursor()
        # # cursor = await pool.cursor()# sql插入语法
        # sql = 'INSERT INTO qiche(id, author, intro, fans, works) values(%s, %s, %s, %s, %s)'
        # try:
        #     # print(sql, (0, item['authors'], item['title'], item['score']))
        #     await cursor.execute(sql, (
        #         0, item['author'], item['intro'], item['fans'], item['works']))
        #     # 提交到数据库执行
        #     await conn.commit()
        #     print('数据插入成功...')
        # except Exception as e:
        #     print(f'数据插入失败: {e}')
        #     # 如果发生错误就回滚
        #     await conn.rollback()





    async def main(self):
        # 异步创建连接池
        conn = await aiomysql.create_pool(host='127.0.0.1', port=3306, user='root', password='root', db='spiders16',
                                      loop=loop)
        # 链接池创建链接的方式
        acq = await conn.acquire()
        cursor = await acq.cursor()

        # 使用预处理语句创建表
        create_sql = '''
                            CREATE TABLE IF NOT EXISTS qiche(
                                id int primary key auto_increment not null,
                                author VARCHAR(255) NOT NULL,
                                intro VARCHAR(255) NOT NULL,
                                fans VARCHAR(255) NOT NULL,
                                works VARCHAR(255) NOT NULL
                                );
                            '''
        # 执行sql
        await cursor.execute(create_sql)
        async with aiohttp.ClientSession(headers=self.headers) as client:
            tasks = []
            for i in range(1, 50):
                res = self.get_data(i, client, conn)
                task = asyncio.create_task(res)
                tasks.append(task)
            await asyncio.wait(tasks)

        await cursor.close()
        conn.close()


if __name__ == '__main__':
    qc = QiChe()
    # asyncio.run()
    # 声明事件循环
    loop = asyncio.get_event_loop()
    loop.run_until_complete(qc.main())
