import asyncio

class TaskManager:
    def __init__(self, max_concurrent_tasks):
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.tasks = []

    async def add_task(self, coroutine):
        async with self.semaphore:
            task = asyncio.create_task(coroutine)
            self.tasks.append(task)
            await task

    async def run_all(self):
        await asyncio.gather(*self.tasks)