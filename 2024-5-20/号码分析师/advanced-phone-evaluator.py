from dataclasses import dataclass
from typing import Dict, List, Optional, Set
import re
from enum import Enum

@dataclass
class PhoneAnalysisDetail:
    """详细的号码分析结果"""
    pattern_matches: Dict[str, List[str]]
    number_meanings: Dict[str, str]
    found_combinations: Dict[str, str]
    operator_info: str
    feng_shui_analysis: Dict[str, str]
    numerology_reading: str
    career_suggestion: List[str]
    personality_traits: List[str]
    value_factors: Dict[str, float]

@dataclass
class PhoneAnalysisResult:
    """完整的分析结果"""
    valid: bool
    phone_number: str
    value_score: float = 0
    value_grade: str = ""
    estimated_value: float = 0
    details: Optional[PhoneAnalysisDetail] = None
    message: str = ""

class NumberType(Enum):
    """号码类型枚举"""
    MOBILE = "mobile"
    UNICOM = "unicom"
    TELECOM = "telecom"

class AdvancedPhoneEvaluator:
    """增强版电话号码评估器"""
    
    def __init__(self, phone_number: str, config: dict):
        self.phone_number = phone_number
        self.config = config
        self.score = 0
        self.details = PhoneAnalysisDetail(
            pattern_matches={},
            number_meanings={},
            found_combinations={},
            operator_info="",
            feng_shui_analysis={},
            numerology_reading="",
            career_suggestion=[],
            personality_traits=[],
            value_factors={}
        )

    def is_valid_number(self) -> bool:
        """验证号码格式"""
        return bool(re.match(r'^1[3-9]\d{9}$', self.phone_number))

    def identify_operator(self) -> NumberType:
        """识别运营商"""
        prefix = self.phone_number[:3]
        if prefix in self.config["operator_prefixes"]["mobile"]:
            return NumberType.MOBILE
        elif prefix in self.config["operator_prefixes"]["unicom"]:
            return NumberType.UNICOM
        else:
            return NumberType.TELECOM

    def analyze_patterns(self) -> None:
        """分析号码模式"""
        for pattern_type, patterns in self.config["special_patterns"].items():
            matches = []
            for pattern in patterns:
                if pattern in self.phone_number:
                    matches.append(pattern)
                    self.score += self.config["pattern_scores"][pattern_type]
            if matches:
                self.details.pattern_matches[pattern_type] = matches
                self.details.value_factors[f"模式_{pattern_type}"] = self.config["pattern_scores"][pattern_type]

    def analyze_repeated_sequences(self) -> None:
        """分析重复序列"""
        for length in range(5, 1, -1):
            sequences = set()
            for i in range(len(self.phone_number) - length + 1):
                substr = self.phone_number[i:i+length]
                if self.phone_number.count(substr) > 1:
                    sequences.add(substr)
            if sequences:
                bonus = self.config["value_factors"]["repeated_sequence"].get(length, 0)
                self.score += bonus
                self.details.value_factors[f"重复序列_{length}位"] = bonus

    def analyze_symmetry(self) -> None:
        """分析对称性"""
        for i in range(len(self.phone_number) - 1):
            if (self.phone_number[i:i+2] == 
                self.phone_number[i:i+2][::-1]):
                self.score += self.config["value_factors"]["symmetric"]
                self.details.value_factors["对称性"] = self.config["value_factors"]["symmetric"]
                break

    def analyze_sequence_pattern(self) -> None:
        """分析数字序列特征"""
        digits = [int(d) for d in self.phone_number[3:]]  # 跳过前缀
        is_ascending = all(digits[i] <= digits[i+1] for i in range(len(digits)-1))
        is_descending = all(digits[i] >= digits[i+1] for i in range(len(digits)-1))
        
        if is_ascending:
            self.score += self.config["value_factors"]["ascending"]
            self.details.value_factors["升序"] = self.config["value_factors"]["ascending"]
        elif is_descending:
            self.score += self.config["value_factors"]["descending"]
            self.details.value_factors["降序"] = self.config["value_factors"]["descending"]

    def analyze_number_meanings(self) -> None:
        """分析数字含义"""
        for digit in set(self.phone_number):
            if digit in self.config["number_meanings"]:
                self.details.number_meanings[digit] = self.config["number_meanings"][digit]

    def find_special_combinations(self) -> None:
        """查找特殊数字组合"""
        for combo, meaning in self.config["number_combinations"].items():
            if combo in self.phone_number:
                self.details.found_combinations[combo] = meaning
                if combo in self.config["value_factors"]["golden_numbers"]:
                    self.score += self.config["value_factors"]["golden_number_bonus"]
                    self.details.value_factors[f"黄金组合_{combo}"] = self.config["value_factors"]["golden_number_bonus"]

    def analyze_feng_shui(self) -> None:
        """分析风水属性"""
        digit_counts = {d: self.phone_number.count(d) for d in set(self.phone_number)}
        element_scores = {element: 0 for element in self.config["feng_shui"]["elements"]}
        
        # 计算五行分数
        for element, numbers in self.config["feng_shui"]["element_scores"].items():
            element_scores[element] = sum(digit_counts.get(num, 0) for num in numbers)
        
        # 找出主要五行
        main_element = max(element_scores.items(), key=lambda x: x[1])[0]
        lucky_elements = self.config["feng_shui"]["lucky_elements"][main_element]
        
        self.details.feng_shui_analysis = {
            "主要五行": main_element,
            "吉利五行": lucky_elements,
            "五行分数": element_scores
        }

    def analyze_numerology(self) -> None:
        """分析数字命理"""
        life_number = sum(int(d) for d in self.phone_number)
        while life_number > 9:
            life_number = sum(int(d) for d in str(life_number))
            
        self.details.numerology_reading = self.config["numerology"]["life_numbers"][str(life_number)]

    def suggest_career(self) -> None:
        """职业建议"""
        digit_set = set(self.phone_number)
        career_matches = []
        
        for career, numbers in self.config["career_readings"].items():
            if any(num in digit_set for num in numbers):
                career_matches.append(career)
                
        self.details.career_suggestion = career_matches

    def analyze_personality(self) -> None:
        """性格特征分析"""
        traits = set()
        for digit in set(self.phone_number):
            if digit in self.config["personality_traits"]:
                traits.update(self.config["personality_traits"][digit])
        self.details.personality_traits = list(traits)

    def calculate_value_grade(self) -> str:
        """计算号码等级"""
        thresholds = self.config["value_grade_th