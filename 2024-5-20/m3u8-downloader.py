import requests
import m3u8
import os
import re
import json
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urljoin, urlparse, parse_qs
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def is_valid_url(url):
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except ValueError:
        return False

def is_valid_filepath(filepath):
    filename = os.path.basename(filepath)
    if not re.match(r'^[\w\-. ]+$', filename):
        return False

    directory = os.path.dirname(filepath)
    if directory and not os.path.exists(directory):
        try:
            os.makedirs(directory, exist_ok=True)
        except OSError:
            return False
    return True

def download_file(url, output_path, headers=None):
    try:
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)
        response.raise_for_status()
        with open(output_path, 'wb') as f:
            f.write(response.content)
        return True
    except requests.RequestException as e:
        logging.error(f"下载文件 {url} 时发生错误: {str(e)}")
        return False

def decrypt_file(input_path, output_path, key, iv):
    try:
        with open(input_path, 'rb') as infile, open(output_path, 'wb') as outfile:
            cipher = AES.new(key, AES.MODE_CBC, iv)
            encrypted_data = infile.read()
            decrypted_data = unpad(cipher.decrypt(encrypted_data), AES.block_size)
            outfile.write(decrypted_data)
    except (ValueError, KeyError) as e:
        logging.error(f"解密文件 {input_path} 时发生错误: {str(e)}")

def get_m3u8_content(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Referer': 'https://kc.zhixueyun.com/',
    }
    cookies = {
        'Hm_lvt_64997e51297683c4a109b5fde52308f1': '**********,**********,**********',
        'Hm_lvt_e5bae42688641fe2fe03e67bcf4dbcb3': '**********',
        'HMACCOUNT': '3365371431026BE3',
        'orgId': '',
        'configId': '',
        'authorization': 'Bearer __ac7c7436f9b190770e17713bb25b6295',
        'Hm_lpvt_e5bae42688641fe2fe03e67bcf4dbcb3': '**********'
    }
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)

    access_token = query_params.get('access_token', [''])[0]
    attachment_id = query_params.get('attachmentId', [''])[0]
    print(access_token, attachment_id)

    # 更新API端点为正确的路径
    api_url = f"{parsed_url.scheme}://{parsed_url.netloc}/api/v1/tools-center-v2/file-cloud/preview"

    headers['Authorization'] = f'Bearer {access_token}'
    params = {'attachmentId': attachment_id}

    try:
        response = requests.get(api_url, headers=headers, params=params, cookies=cookies)
        if response.status_code == 404:
            logging.error("资源未找到，请检查attachmentId是否正确。")
            return None
        elif response.status_code == 401:
            logging.error("未授权的访问，请检查access_token是否有效。")
            return None
        response.raise_for_status()
        data = response.json()
        if 'data' in data and 'fileUrl' in data['data']:
            m3u8_url = data['data']['fileUrl']
            # 获取m3u8内容
            m3u8_response = requests.get(m3u8_url, headers=headers)
            m3u8_response.raise_for_status()
            return m3u8_response.text
        else:
            raise ValueError("API响应中未找到m3u8文件的URL")
    except requests.RequestException as e:
        logging.error(f"获取m3u8内容时发生错误: {str(e)}")
        if 'response' in locals():
            logging.error(f"服务器响应: {response.text}")
        return None
    except ValueError as e:
        logging.error(str(e))
        return None

def download_and_merge_m3u8(m3u8_url, output_file):
    m3u8_content = get_m3u8_content(m3u8_url)
    if not m3u8_content:
        return

    try:
        logging.info("正在解析m3u8文件...")
        playlist = m3u8.loads(m3u8_content)
        logging.info(f"类型 of playlist.keys: {type(playlist.keys)}")
        logging.info(f"playlist.keys 内容: {playlist.keys}")
    except Exception as e:
        logging.error(f"解析m3u8文件时发生错误: {str(e)}")
        return

    if not playlist.segments:
        logging.error("m3u8文件中没有找到任何片段。")
        return

    # 将 playlist.keys 转换为列表
    keys_list = list(playlist.keys)

    if keys_list:
        key = keys_list[0]
        key_url = key.uri
        iv = bytes.fromhex(key.iv[2:]) if key.iv else None
    else:
        key_url = None
        key = None
        iv = None

    headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    }

    if key_url:
        logging.info("正在下载解密密钥...")
        key_path = 'decryption.key'
        if not download_file(key_url, key_path, headers):
            logging.error("无法下载解密密钥，退出。")
            return
        with open(key_path, 'rb') as key_file:
            key_bytes = key_file.read()
        os.remove(key_path)
    else:
        logging.info("未找到加密信息，将以未加密方式处理。")
        key_bytes = None

    temp_dir = 'temp_ts_files'
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    logging.info(f"开始下载 {len(playlist.segments)} 个ts文件...")

    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for i, segment in enumerate(playlist.segments):
            ts_url = urljoin(m3u8_url, segment.uri)
            encrypted_path = os.path.join(temp_dir, f'{i}_encrypted.ts')
            decrypted_path = os.path.join(temp_dir, f'{i}.ts')
            futures.append(executor.submit(download_file, ts_url, encrypted_path, headers))

        for i, future in enumerate(futures):
            try:
                if future.result():
                    if key_bytes:
                        decrypt_file(os.path.join(temp_dir, f'{i}_encrypted.ts'),
                                     os.path.join(temp_dir, f'{i}.ts'), key_bytes, iv)
                        os.remove(os.path.join(temp_dir, f'{i}_encrypted.ts'))
                    else:
                        os.rename(os.path.join(temp_dir, f'{i}_encrypted.ts'),
                                  os.path.join(temp_dir, f'{i}.ts'))
                    logging.info(f"已下载并处理 {i + 1}/{len(playlist.segments)} 个文件")
                else:
                    logging.warning(f"文件 {i + 1} 下载失败，继续下载其他文件...")
            except Exception as e:
                logging.error(f"处理文件 {i + 1} 时发生错误: {str(e)}")

    logging.info("所有可用的ts文件下载完成。开始合并...")

    try:
        with open(output_file, 'wb') as outfile:
            for i in range(len(playlist.segments)):
                ts_path = os.path.join(temp_dir, f'{i}.ts')
                if os.path.exists(ts_path):
                    with open(ts_path, 'rb') as infile:
                        outfile.write(infile.read())
                else:
                    logging.warning(f"警告: 文件 {ts_path} 不存在，已跳过。")

        logging.info(f"合并完成。输出文件: {output_file}")
    except IOError as e:
        logging.error(f"合并文件时发生错误: {str(e)}")
    finally:
        # 清理临时文件
        for file in os.listdir(temp_dir):
            try:
                os.remove(os.path.join(temp_dir, file))
            except Exception as e:
                logging.warning(f"无法删除临时文件 {file}: {str(e)}")
        try:
            os.rmdir(temp_dir)
        except Exception as e:
            logging.warning(f"无法删除临时目录 {temp_dir}: {str(e)}")

def main():
    logging.info("欢迎使用加密m3u8下载器!")

    while True:
        m3u8_url = input("请输入m3u8文件的URL (或输入'q'退出): ").strip()
        if m3u8_url.lower() == 'q':
            logging.info("感谢使用，再见！")
            break

        if not is_valid_url(m3u8_url):
            logging.error("无效的URL，请重新输入。")
            continue

        output_file = input("请输入输出文件的完整路径 (例如 /Users/<USER>/Downloads/output.mp4): ").strip()
        if not is_valid_filepath(output_file):
            logging.error("无效的文件路径，请确保文件名合法且目录可写入。")
            continue

        try:
            download_and_merge_m3u8(m3u8_url, output_file)
        except Exception as e:
            logging.error(f"发生未预期的错误: {str(e)}")

        print("\n是否要下载另一个文件？")

if __name__ == "__main__":
    main()
