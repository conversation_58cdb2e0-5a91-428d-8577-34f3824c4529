import React, { useState } from 'react';
import { Upload, Button, Input, message, Space, Modal, Steps, Spin, Result } from 'antd';
import { UploadOutlined, InboxOutlined, FileDoneOutlined, SyncOutlined, CheckCircleOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Dragger } = Upload;
const { Step } = Steps;

const FileUploader = ({ onUploadSuccess }) => {
  const [fileList, setFileList] = useState([]);
  const [collectionName, setCollectionName] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // New state for upload progress
  const [uploadStatus, setUploadStatus] = useState('idle'); // idle, uploading, processing, vectorizing, success, error
  const [currentStep, setCurrentStep] = useState(0); // For Steps component
  const [uploadError, setUploadError] = useState('');
  const [responseData, setResponseData] = useState(null); // Store response data

  const showModal = () => {
    setIsModalOpen(true);
    setUploadStatus('idle');
    setCurrentStep(0);
    setFileList([]);
    setCollectionName('');
    setUploadError('');
    setResponseData(null);
  };

  const handleCancel = () => {
    // Only allow cancel if not in a final state
    if (!['success', 'error'].includes(uploadStatus)) {
      setIsModalOpen(false);
    }
  };
  
  // Simulate async delay
  const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  const handleUpload = async () => {
    const formData = new FormData();
    fileList.forEach(file => {
      formData.append('file', file);
    });
    
    if (collectionName.trim()) {
      formData.append('collection_name', collectionName.trim());
    }
    
    setUploadStatus('uploading');
    setCurrentStep(0);
    setUploadError('');
    setResponseData(null);
    
    try {
      // Step 1: Uploading
      const response = await api.uploadFile(formData);
      setResponseData(response.data); // Store response data
      message.success('文件上传成功 / File uploaded successfully');
      
      // Step 2: Processing (Simulated)
      setUploadStatus('processing');
      setCurrentStep(1);
      await wait(1500); // Simulate processing time
      
      // Step 3: Vectorizing (Simulated)
      setUploadStatus('vectorizing');
      setCurrentStep(2);
      await wait(2000); // Simulate vectorizing time
      
      // Step 4: Success
      setUploadStatus('success');
      setCurrentStep(3);
      
      // Call the success callback after all steps
      if (onUploadSuccess) {
        onUploadSuccess(response.data);
      }
      
      // Optionally close modal after a delay on success
      // setTimeout(() => setIsModalOpen(false), 2000);
      
    } catch (error) {
      console.error('Upload error:', error);
      const errorDetail = error?.response?.data?.detail || error.message || 'File upload failed';
      setUploadError(errorDetail);
      setUploadStatus('error');
      message.error(`上传失败: ${errorDetail}`);
    } 
    // Note: We don't set uploading to false here anymore, status handles the state
  };

  const props = {
    onRemove: file => {
      // Only allow removal if in idle state
      if (uploadStatus === 'idle') {
        const index = fileList.indexOf(file);
        const newFileList = fileList.slice();
        newFileList.splice(index, 1);
        setFileList(newFileList);
      }
    },
    beforeUpload: file => {
      // Only allow adding files if in idle state
      if (uploadStatus !== 'idle') {
        return false;
      }
      
      const allowedTypes = [
        'application/pdf',
        'text/plain',
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        message.error('You can only upload PDF, TXT, CSV, Word (DOC/DOCX), or Excel files!');
        return Upload.LIST_IGNORE;
      }
      
      setFileList([file]); // Allow only single file upload for clarity
      return false;
    },
    fileList,
    multiple: false, // Enforce single file upload
    disabled: uploadStatus !== 'idle', // Disable dragger when not idle
  };

  const getModalFooter = () => {
    if (uploadStatus === 'success') {
      return [
        <Button key="done" type="primary" onClick={() => setIsModalOpen(false)}>
          完成 / Done
        </Button>
      ];
    } else if (uploadStatus === 'error') {
      return [
        <Button key="back" onClick={showModal}> {/* Reset to try again */}
          返回 / Back
        </Button>
      ];
    } else if (['uploading', 'processing', 'vectorizing'].includes(uploadStatus)) {
       return [
        <Button key="back" disabled>
          取消 / Cancel
        </Button>,
        <Button key="submit" type="primary" loading disabled>
          上传中 / Uploading...
        </Button>
       ];
    } else { // idle state
      return [
        <Button key="back" onClick={handleCancel}>
          取消 / Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleUpload}
          disabled={fileList.length === 0}
        >
          上传 / Upload
        </Button>,
      ];
    }
  }

  return (
    <>
      <Button 
        type="primary" 
        icon={<UploadOutlined />} 
        onClick={showModal}
        // style={{ marginBottom: '16px' }} // Removed style, can be handled by parent
      >
        上传文档 / Upload Document
      </Button>
      
      <Modal
        title="上传文档 / Upload Document"
        open={isModalOpen}
        onCancel={handleCancel}
        footer={getModalFooter()}
        maskClosable={uploadStatus === 'idle'} // Prevent closing during processing
      >
        {uploadStatus === 'idle' && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Dragger {...props}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传 / Click or drag file to this area to upload</p>
              <p className="ant-upload-hint">
                支持 PDF, TXT, CSV, Word (DOC/DOCX), 和 Excel 文件 / Support for PDF, TXT, CSV, Word (DOC/DOCX), and Excel files.
              </p>
            </Dragger>
            
            <Input
              placeholder="集合名称（可选，留空将自动生成）/ Collection name (optional, auto-generated if empty)"
              value={collectionName}
              onChange={e => setCollectionName(e.target.value)}
              style={{ marginTop: '16px' }}
              disabled={uploadStatus !== 'idle'}
            />
          </Space>
        )}
        
        {!['idle', 'success', 'error'].includes(uploadStatus) && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" />
            <Steps current={currentStep} style={{ marginTop: 20 }}>
              <Step title="上传中 / Uploading" />
              <Step title="处理中 / Processing" />
              <Step title="向量化 / Vectorizing" />
              <Step title="完成 / Done" />
            </Steps>
          </div>
        )}
        
        {uploadStatus === 'success' && (
          <Result
            status="success"
            title="上传处理成功! / Upload Processed Successfully!"
            subTitle={`文件 '${responseData?.filename || fileList[0]?.name}' 已成功处理并添加到集合 '${responseData?.collection_name}' (${responseData?.document_count} 文档块). / File '${responseData?.filename || fileList[0]?.name}' processed and added to collection '${responseData?.collection_name}' (${responseData?.document_count} chunks).`}
            icon={<CheckCircleOutlined />}
          />
        )}
        
        {uploadStatus === 'error' && (
           <Result
            status="error"
            title="上传失败 / Upload Failed"
            subTitle={uploadError || 'An unknown error occurred during upload.'}
          />
        )}
      </Modal>
    </>
  );
};

export default FileUploader;