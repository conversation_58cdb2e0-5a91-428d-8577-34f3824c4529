import React from 'react';
import { Ava<PERSON>, Typography, Card, Space, Button, message, Tooltip, Tag, Spin } from 'antd';
import { UserOutlined, RobotOutlined, CopyOutlined, CheckOutlined, DatabaseOutlined, InfoCircleOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

const { Text } = Typography;

const MessageItem = ({ message, type, model, modelType, isLoading, isError, usedLocalKnowledge, collection, webSearchEngine, timestamp, onRetry }) => {
  const isUser = type === 'user';
  const [copied, setCopied] = React.useState(false);
  
  // Format model display name
  const getModelDisplayName = () => {
    if (!model) return null;
    
    // Extract the base name for display
    const baseName = model.includes('/') ? model.split('/').pop() : model;
    const displayName = baseName.replace(/[-_:]/g, ' ');
    
    return (
      <Tag 
        color={modelType === 'deepseek' ? 'blue' : (modelType === 'mcp' ? 'green' : 'orange')}
        style={{ 
          fontSize: '10px', 
          padding: '0 6px', 
          marginLeft: '8px',
          borderRadius: '10px',
          textTransform: 'capitalize'
        }}
      >
        {modelType === 'deepseek' ? 'API: ' : (modelType === 'mcp' ? 'MCP: ' : '')}
        {displayName}
      </Tag>
    );
  };
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message)
      .then(() => {
        setCopied(true);
        message.success('已复制到剪贴板 / Copied to clipboard');
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        console.error('复制失败 / Failed to copy: ', err);
        message.error('复制失败 / Failed to copy');
      });
  };
  
  // 联网结果标签
  const getWebSearchTag = () => {
    if (!webSearchEngine) return null;
    let label = '';
    switch (webSearchEngine) {
      case 'bing': label = 'Bing 联网'; break;
      case 'google': label = 'Google 联网'; break;
      case 'baidu': label = 'Baidu 联网'; break;
      default: label = 'DuckDuckGo 联网';
    }
    return <Tag color="green" style={{ marginLeft: 8 }}>{label}</Tag>;
  };
  
  return (
    <div
      className="message-item-container"
      style={{
        display: 'flex',
        flexDirection: isUser ? 'row-reverse' : 'row',
        alignItems: 'flex-end',
        marginBottom: '20px',
        position: 'relative',
        opacity: isLoading ? 0.7 : 1,
        transition: 'opacity 0.3s ease-in-out',
        animation: 'fadeInMessage 0.3s ease-in-out'
      }}
    >
      <Avatar
        size={48}
        icon={isUser ? <UserOutlined /> : <RobotOutlined />}
        style={{
          background: isUser ? 'linear-gradient(135deg, #1890ff 60%, #40a9ff 100%)' : 'linear-gradient(135deg, #f56a00 60%, #faad14 100%)',
          marginRight: isUser ? 0 : '12px',
          marginLeft: isUser ? '12px' : 0,
          boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
        }}
      />
      
      <Card
        className={`message-card ${isUser ? 'user-message-card' : 'ai-message-card'}`}
        style={{
          maxWidth: '85%',
          background: isUser
            ? 'linear-gradient(135deg, #e6f7ff 80%, #f0f5ff 100%)'
            : (isError
              ? 'linear-gradient(135deg, #fff1f0 80%, #fffbe6 100%)'
              : 'linear-gradient(135deg, #fff 80%, #f6ffed 100%)'),
          borderRadius: '16px',
          borderTopLeftRadius: isUser ? '16px' : '6px',
          borderTopRightRadius: isUser ? '6px' : '16px',
          position: 'relative',
          boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
          border: isUser ? '1px solid #d6e4ff' : (isError ? '1px solid #ffccc7' : '1px solid #f0f0f0'),
        }}
        bodyStyle={{ padding: '14px 20px' }}
      >
        {isUser ? (
          <div>
            <Text style={{ fontSize: '15px', color: '#333' }}>{message}</Text>
            
            <div className="message-actions" style={{ 
              position: 'absolute', 
              right: '8px', 
              bottom: '4px',
              opacity: 0,
              transition: 'opacity 0.2s',
            }}>
              <Tooltip title="复制 / Copy">
                <Button 
                  type="text" 
                  size="small" 
                  icon={copied ? <CheckOutlined style={{ color: '#52c41a' }} /> : <CopyOutlined />}
                  onClick={copyToClipboard}
                />
              </Tooltip>
            </div>
          </div>
        ) : (
          <div>
            <div style={{ marginBottom: '6px', display: 'flex', alignItems: 'center' }}>
              {!isUser && model && getModelDisplayName()}
              {getWebSearchTag()}
              {isLoading && <Spin size="small" style={{ marginLeft: '8px' }} />}
              {!isUser && usedLocalKnowledge && (
                <Tooltip title="使用本地知识库增强回答质量 / Using local vector database to enhance response">
                  <Tag 
                    color="success" 
                    icon={<DatabaseOutlined style={{ fontSize: '12px' }} />}
                    style={{ 
                      fontSize: '11px', 
                      padding: '3px 10px', 
                      marginLeft: '8px',
                      borderRadius: '12px',
                      fontWeight: 'bold',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '5px',
                      boxShadow: '0 2px 4px rgba(0,128,0,0.15)',
                      border: '1px solid #52c41a',
                      background: 'linear-gradient(to right, #f6ffed, #d9f7be)'
                    }}
                  >
                    知识库匹配 / Local Knowledge
                  </Tag>
                </Tooltip>
              )}
            </div>
            
            {isError ? (
              <div>
                <Text type="danger">{message}</Text>
                {onRetry && (
                  <Button type="link" size="small" onClick={onRetry} style={{ marginLeft: 8 }}>
                    重试 / Retry
                  </Button>
                )}
              </div>
            ) : (
              <ReactMarkdown
                className="markdown-content"
                components={{
                  code({ node, inline, className, children, ...props }) {
                    const match = /language-(\w+)/.exec(className || '');
                    return !inline && match ? (
                      <div style={{ position: 'relative', marginTop: '16px', marginBottom: '16px' }}>
                        <Tag color="blue" style={{ position: 'absolute', left: 8, top: 8, zIndex: 10, fontSize: 11, opacity: 0.85 }}>
                          {match[1].toUpperCase()}
                        </Tag>
                        <div style={{ 
                          position: 'absolute', 
                          top: '8px', 
                          right: '8px', 
                          zIndex: 10,
                          opacity: 0.8
                        }}>
                          <Tooltip title="复制代码 / Copy code">
                            <Button 
                              type="text" 
                              size="small" 
                              icon={<CopyOutlined style={{ color: '#fff' }} />}
                              onClick={() => {
                                navigator.clipboard.writeText(String(children).replace(/\n$/, ''));
                                message.success('代码已复制 / Code copied');
                              }}
                              style={{ color: '#fff', background: 'rgba(0,0,0,0.2)' }}
                            />
                          </Tooltip>
                        </div>
                        <SyntaxHighlighter
                          style={atomDark}
                          language={match[1]}
                          PreTag="div"
                          showLineNumbers={true}
                          wrapLines={true}
                          customStyle={{
                            borderRadius: '8px',
                            padding: '12px',
                            fontSize: '14px',
                          }}
                          {...props}
                        >
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      </div>
                    ) : (
                      <code className={className} {...props} style={{
                        backgroundColor: '#f5f5f5',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        color: '#d56161',
                        fontFamily: 'monospace'
                      }}>
                        {children}
                      </code>
                    );
                  },
                  p: ({ node, children }) => (
                    <p style={{ 
                      margin: '8px 0', 
                      lineHeight: '1.6',
                      fontSize: '15px',
                      color: '#333'
                    }}>
                      {children}
                    </p>
                  ),
                  h1: ({ node, children }) => (
                    <h1 style={{ 
                      borderBottom: '1px solid #eee',
                      paddingBottom: '4px',
                      marginTop: '24px',
                      marginBottom: '16px',
                      fontSize: '24px',
                      fontWeight: 'bold' 
                    }}>
                      {children}
                    </h1>
                  ),
                  h2: ({ node, children }) => (
                    <h2 style={{ 
                      borderBottom: '1px solid #eee',
                      paddingBottom: '4px',
                      marginTop: '20px',
                      marginBottom: '14px',
                      fontSize: '20px',
                      fontWeight: 'bold' 
                    }}>
                      {children}
                    </h2>
                  ),
                  h3: ({ node, children }) => (
                    <h3 style={{ 
                      marginTop: '16px',
                      marginBottom: '12px',
                      fontSize: '18px',
                      fontWeight: 'bold' 
                    }}>
                      {children}
                    </h3>
                  ),
                  ul: ({ node, children }) => (
                    <ul style={{ 
                      paddingLeft: '20px',
                      margin: '12px 0'
                    }}>
                      {children}
                    </ul>
                  ),
                  ol: ({ node, children }) => (
                    <ol style={{ 
                      paddingLeft: '20px',
                      margin: '12px 0'
                    }}>
                      {children}
                    </ol>
                  ),
                  li: ({ node, children }) => (
                    <li style={{ 
                      margin: '4px 0'
                    }}>
                      {children}
                    </li>
                  ),
                  blockquote: ({ node, children }) => (
                    <blockquote style={{ 
                      borderLeft: '4px solid #52c41a',
                      background: 'linear-gradient(90deg, #f6ffed 80%, #e6f7ff 100%)',
                      paddingLeft: '16px',
                      margin: '16px 0',
                      color: '#666',
                      fontStyle: 'italic',
                      borderRadius: '6px',
                    }}>
                      {children}
                    </blockquote>
                  ),
                }}
              >
                {message}
              </ReactMarkdown>
            )}
            
            {!isLoading && !isError && message && (
              <div className="message-actions" style={{ 
                position: 'absolute', 
                right: '8px', 
                bottom: '4px',
                opacity: 1,
                transition: 'opacity 0.2s',
              }}>
                <Tooltip title="复制 / Copy">
                  <Button 
                    type="text" 
                    size="small" 
                    icon={copied ? <CheckOutlined style={{ color: '#52c41a' }} /> : <CopyOutlined />}
                    onClick={copyToClipboard}
                  />
                </Tooltip>
              </div>
            )}
            
            {/* 时间戳 */}
            {timestamp && (
              <div style={{ textAlign: 'right', marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 11 }}>{timestamp}</Text>
              </div>
            )}
          </div>
        )}
      </Card>
      
      <style jsx="true">{`
        @keyframes fadeInMessage {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .message-card:hover .message-actions {
          opacity: 1;
        }
      `}</style>
    </div>
  );
};

export default MessageItem;