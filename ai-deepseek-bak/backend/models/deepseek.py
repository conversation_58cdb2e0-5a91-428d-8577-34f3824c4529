import os
import httpx
from httpx import HTTPStatusError
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from dotenv import load_dotenv
from models.openai import OpenAIClient

load_dotenv()

class DeepSeekClient:
    """Client for interacting with DeepSeek API"""
    
    def __init__(self):
        self.api_key = os.getenv("DEEPSEEK_API_KEY")
        self.api_url = os.getenv("DEEPSEEK_API_URL", "https://api.deepseek.com/v1")
        
        if not self.api_key:
            print("Warning: DEEPSEEK_API_KEY not set in environment variables")
        
        self.openai_client = OpenAIClient()
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for DeepSeek API requests"""
        if not self.api_key:
            print("Error: No DeepSeek API key available")
        
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def _validate_api_key(self) -> bool:
        """Check if API key looks valid"""
        if not self.api_key:
            return False
            
        # Most API keys start with sk-
        if not self.api_key.startswith("sk-"):
            print("Warning: DeepSeek API key doesn't follow expected format (should start with 'sk-')")
            return False
            
        return True
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """Get available models from DeepSeek API"""
        try:
            if not self.api_key:
                print("Error: Cannot list models - no DeepSeek API key provided")
                # Return default models if no API key
                return [
                    {"id": "deepseek-chat", "name": "DeepSeek Chat"},
                    {"id": "deepseek-coder", "name": "DeepSeek Coder"}
                ]
                
            print(f"Fetching DeepSeek models from {self.api_url}/models")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(
                    f"{self.api_url}/models",
                    headers=self._get_headers()
                )
                response.raise_for_status()
                data = response.json()
                
                # Debug output
                print(f"DeepSeek API models response status: {response.status_code}")
                print(f"DeepSeek API models data: {json.dumps(data)[:500]}...")
                
                models = data.get("data", [])
                
                # If no models returned, use fallback defaults
                if not models:
                    print("No models returned from DeepSeek API, using defaults")
                    return [
                        {"id": "deepseek-chat", "name": "DeepSeek Chat"},
                        {"id": "deepseek-coder", "name": "DeepSeek Coder"}
                    ]
                
                # Format models for frontend use
                formatted_models = []
                for model in models:
                    model_id = model.get("id", "")
                    formatted_models.append({
                        "id": model_id,
                        "name": model.get("name", model_id),
                        "created": model.get("created", "unknown")
                    })
                
                return formatted_models
        except HTTPStatusError as e:
            error_detail = f"HTTP error {e.response.status_code}: {e.response.text}"
            print(f"DeepSeek API error when listing models: {error_detail}")
            # Return default models on error
            return [
                {"id": "deepseek-chat", "name": "DeepSeek Chat"},
                {"id": "deepseek-coder", "name": "DeepSeek Coder"}
            ]
        except Exception as e:
            print(f"Error listing DeepSeek models: {str(e)}")
            # Return default models on error
            return [
                {"id": "deepseek-chat", "name": "DeepSeek Chat"},
                {"id": "deepseek-coder", "name": "DeepSeek Coder"}
            ]
    
    async def generate(
        self, 
        prompt: str,
        context: str = "",
        model: str = "deepseek-chat",
        temperature: float = 0.7,
        max_tokens: int = 1024,
        stream: bool = False
    ) -> AsyncGenerator[str, None]:
        """Generate response from DeepSeek model, optionally streaming"""
        
        if not self._validate_api_key():
            error_msg = "No DeepSeek API key provided or invalid format. Please configure it in settings."
            print(error_msg)
            # Yield the error message for both stream and non-stream cases
            yield error_msg
            return # Stop further execution
        
        messages = []
        if context:
            messages.append({
                "role": "system",
                "content": f"You have access to the following relevant information...\n\n{context}"
            })
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }
        
        try:
            print(f"Sending request to DeepSeek API (Stream={stream}): {self.api_url}/chat/completions")
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream(
                    "POST",
                    f"{self.api_url}/chat/completions",
                    headers=self._get_headers(),
                    json=payload
                ) as response:
                    await response.aread() # Need to read headers first
                    if response.status_code != 200:
                         # Read the error body for more details
                         error_body = await response.aread()
                         error_detail = f"HTTP error {response.status_code}: {error_body.decode()}"
                         print(f"DeepSeek API error: {error_detail}")
                         yield f"Error communicating with DeepSeek API: {error_detail}"
                         return
                         
                    async for line in response.aiter_lines():
                        print(f"DeepSeek raw stream line: {line}")
                        if line.startswith("data: "):
                            line_data = line[len("data: "):]  # 去掉前缀
                            if line_data.strip() == "[DONE]":
                                break
                            try:
                                chunk = json.loads(line_data)
                                content = (
                                    chunk.get("choices", [{}])[0].get("delta", {}).get("content", "") or
                                    chunk.get("choices", [{}])[0].get("message", {}).get("content", "") or
                                    chunk.get("choices", [{}])[0].get("text", "")
                                )
                                if content:
                                    print(f"DeepSeek parsed content: {content}")
                                    yield content
                            except json.JSONDecodeError:
                                print(f"Warning: Could not decode JSON chunk: {line_data}")
                                continue # Skip invalid chunks
                                
        except HTTPStatusError as e:
            error_info = f"HTTP error {e.response.status_code}: {e.response.text}"
            print(f"DeepSeek API error: {error_info}")
            yield f"Error communicating with DeepSeek API: {error_info}"
        except Exception as e:
            error_msg = str(e)
            print(f"Unexpected error with DeepSeek API: {error_msg}")
            yield f"Unexpected error with DeepSeek API: {error_msg}"

    async def chat_stream(self, model, system_prompt, user_prompt, temperature=0.7, max_tokens=1024):
        """
        chat_stream 是 generate 的别名，兼容主程序调用
        """
        context = system_prompt if system_prompt else ""
        prompt = user_prompt
        async for chunk in self.generate(prompt, context=context, model=model, temperature=temperature, max_tokens=max_tokens, stream=True):
            yield chunk