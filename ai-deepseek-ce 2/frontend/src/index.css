body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.message-container {
  max-height: calc(100vh - 240px);
  overflow-y: auto;
  padding: 20px;
}

.user-message {
  background-color: #e6f7ff;
  border-radius: 8px;
  padding: 10px 15px;
  margin-bottom: 15px;
  max-width: 80%;
  align-self: flex-end;
  margin-left: auto;
}

.ai-message {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px 15px;
  margin-bottom: 15px;
  max-width: 80%;
}

.file-upload-container {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.file-upload-container:hover {
  border-color: #1890ff;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #999;
}