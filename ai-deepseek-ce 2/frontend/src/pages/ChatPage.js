import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Spin, Alert, Typography, Row, Col, Empty, Divider, Button, Switch, Form, Select } from 'antd';
import { RobotOutlined, MessageOutlined, FileAddOutlined } from '@ant-design/icons';
import ChatInput from '../components/ChatInput';
import MessageItem from '../components/MessageItem';
import { Link } from 'react-router-dom';
import api from '../services/api';
import { message } from 'antd';

const { Title, Text, Paragraph } = Typography;

const ChatPage = () => {
  const [messages, setMessages] = useState([]);
  const [collections, setCollections] = useState([]);
  const [models, setModels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [modelType, setModelType] = useState('deepseek');
  const [useWebSearch, setUseWebSearch] = useState(false);
  const [webSearchEngine, setWebSearchEngine] = useState('duckduckgo');
  
  const messagesEndRef = useRef(null);

  useEffect(() => {
    fetchCollections();
    fetchModels();
  }, []);

  useEffect(() => {
    fetchModels();
  }, [modelType]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchCollections = async () => {
    try {
      const response = await api.getCollections();
      setCollections(response.data.collections);
    } catch (error) {
      console.error('Error fetching collections:', error);
      setError('无法加载文档集合 / Failed to load document collections');
    }
  };

  const fetchModels = async () => {
    setError(null);
    try {
      // Load settings for both API types
      const ollamaSettings = localStorage.getItem('ollamaSettings');
      const parsedOllamaSettings = ollamaSettings ? JSON.parse(ollamaSettings) : {};
      
      const deepseekSettings = localStorage.getItem('deepseekSettings');
      const parsedDeepseekSettings = deepseekSettings ? JSON.parse(deepseekSettings) : {};
      
      // Configure API settings
      if (parsedOllamaSettings.apiUrl) {
        api.setOllamaApiUrl(parsedOllamaSettings.apiUrl);
      }
      
      if (parsedDeepseekSettings.apiKey && parsedDeepseekSettings.apiUrl) {
        api.setDeepseekConfig(parsedDeepseekSettings.apiKey, parsedDeepseekSettings.apiUrl);
      }
      
      const response = await api.getModels();
      
      if (modelType === 'deepseek') {
        if (response.data.deepseek_models && response.data.deepseek_models.length > 0) {
          setModels(response.data.deepseek_models);
        } else {
          setError('无法加载DeepSeek模型列表，请检查API设置 / Failed to load DeepSeek models, please check API settings');
          setModels([]);
        }
      } else {
        if (response.data.ollama_models && response.data.ollama_models.length > 0) {
          setModels(response.data.ollama_models);
        } else {
          setError('无法加载Ollama模型列表，请检查服务设置 / Failed to load Ollama models, please check service settings');
          setModels([]);
        }
      }
    } catch (error) {
      console.error('Error fetching models:', error);
      setError('无法加载模型列表 / Failed to load models');
      setModels([]);
    }
  };

  const handleSendMessage = async (chatParams) => {
    const { message, model, collection, useRAG, temperature, maxTokens, modelType, prioritizeLocalVectorDB } = chatParams;
    
    // Add user message
    const userMessage = { content: message, type: 'user' };
    setMessages(prev => [...prev, userMessage]);
    
    // Add a placeholder AI message that will be updated
    const aiMessageId = `ai-${Date.now()}`;
    const initialAiMessage = { 
      id: aiMessageId, 
      content: '', // Start with empty content
      type: 'ai', 
      model: model, 
      modelType: modelType,
      isLoading: true // Add loading state
    };
    setMessages(prev => [...prev, initialAiMessage]);
    
    setIsLoading(true);
    setError(null);
    
    const formData = new FormData();
    formData.append('query', message);
    formData.append('model', model);
    if (useRAG && collection) {
      formData.append('collection_name', collection);
      formData.append('use_rag', 'true');
    } else {
      formData.append('use_rag', 'false');
    }
    formData.append('temperature', temperature.toString());
    formData.append('max_tokens', maxTokens.toString());
    formData.append('prioritize_local_vector_db', prioritizeLocalVectorDB.toString());
    formData.append('use_web_search', useWebSearch);
    formData.append('web_search_engine', webSearchEngine);
    
    // Select the appropriate endpoint based on model type
    let endpoint;
    if (modelType === 'mcp') {
      endpoint = '/chat/mcp';
      // For MCP mode, always send the collection name if available
      if (collection) {
        formData.set('collection_name', collection);
      }
    } else if (modelType === 'deepseek') {
      endpoint = '/chat/deepseek';
    } else {
      endpoint = '/chat/ollama';
    }

    // --- Streaming Logic --- 
    let accumulatedContent = '';
    let usedLocalKnowledge = false; // Flag to track if local knowledge was used
    
    const handleChunk = (chunk, metadata) => {
      // Check if this response used local knowledge
      if (metadata && metadata.used_local_knowledge) {
        usedLocalKnowledge = true;
        console.log("Used local knowledge: true, collection:", chatParams.collection);
      }
      
      accumulatedContent += chunk;
      setMessages(prev => prev.map(msg => 
        msg.id === aiMessageId ? { 
          ...msg, 
          content: accumulatedContent, 
          isLoading: true,
          usedLocalKnowledge: usedLocalKnowledge, // Add this flag to the message
          collection: chatParams.collection // Add collection to the message
        } : msg
      ));
    };

    const handleError = (streamError) => {
      console.error('Streaming error:', streamError);
      setError(streamError); // Display the error
      setMessages(prev => prev.map(msg => 
        msg.id === aiMessageId ? { ...msg, content: `发生错误: ${streamError}`, isLoading: false, isError: true } : msg
      ));
      setIsLoading(false);
    };

    const handleComplete = () => {
      setMessages(prev => prev.map(msg => 
        msg.id === aiMessageId ? { 
          ...msg, 
          isLoading: false,
          usedLocalKnowledge: usedLocalKnowledge, // Ensure the flag is preserved
          collection: chatParams.collection // Ensure the collection is preserved
        } : msg
      ));
      setIsLoading(false);
      console.log('Streaming complete');
    };

    await api.sendMessage(
      endpoint, 
      formData, 
      handleChunk,
      handleError,
      handleComplete
    );
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleClearChat = () => {
    if (messages.length > 0) {
      setMessages([]);
      message.success('对话已清空 / Conversation cleared');
    }
  };

  return (
    <Row justify="center">
      <Col xs={24} sm={24} md={20} lg={18} xl={16}>
        <Card 
          style={{ 
            marginBottom: '24px', 
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.08)'
          }}
          headStyle={{ 
            borderBottom: '1px solid #f0f0f0',
            padding: '16px 24px'
          }}
          bodyStyle={{
            padding: 0
          }}
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <RobotOutlined style={{ fontSize: '20px', marginRight: '12px', color: '#1890ff' }} />
                <Title level={4} style={{ margin: 0 }}>与 AI 聊天 / Chat with AI</Title>
              </div>
              {messages.length > 0 && (
                <Button 
                  danger 
                  size="small" 
                  onClick={handleClearChat}
                  disabled={isLoading}
                >
                  清空对话 / Clear Chat
                </Button>
              )}
            </div>
          }
        >
          {error && (
            <Alert 
              message={error} 
              type="error" 
              showIcon 
              closable 
              style={{ margin: '16px', borderRadius: '8px' }} 
            />
          )}
          
          <div 
            className="message-container custom-scrollbar" 
            style={{ 
              height: 'calc(70vh - 200px)', 
              overflowY: 'auto',
              padding: '20px',
              backgroundColor: '#f9f9f9'
            }}
          >
            {messages.length === 0 ? (
              <div style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center', 
                justifyContent: 'center',
                height: '100%',
                textAlign: 'center', 
                color: '#595959',
                padding: '24px'
              }}>
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <span style={{ fontSize: '16px', fontWeight: 'bold' }}>还没有对话记录 / No conversation yet</span>
                  }
                  style={{ marginBottom: '24px' }}
                />
                
                <Card 
                  style={{ 
                    maxWidth: '80%', 
                    marginTop: '12px',
                    borderRadius: '8px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                  }}
                >
                  <Paragraph style={{ fontSize: '14px' }}>
                    <Text strong style={{ fontSize: '16px' }}>欢迎使用 AI-DeepSeek! / Welcome to AI-DeepSeek!</Text>
                    <Divider style={{ margin: '12px 0' }} />
                    <ul style={{ textAlign: 'left', paddingLeft: '20px' }}>
                      <li>可以提问任何问题，AI 将尽力回答 / Ask any question, and the AI will try to answer</li>
                      <li>选择不同的模型以获取不同的回答体验 / Choose different models for different response experiences</li>
                      <li>上传专业文档可以增强 AI 回答的准确性 / Upload professional documents to enhance AI answer accuracy</li>
                    </ul>
                  </Paragraph>
                </Card>
                
                <Divider style={{ margin: '24px 0', width: '80%' }} />
                
                {collections.length === 0 && (
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary" style={{ display: 'block', marginBottom: '12px' }}>
                      提示：上传专业文档可以让 AI 回答更智能 / 
                      Tip: Upload professional documents to make AI answers smarter
                    </Text>
                    <Link to="/documents">
                      <Button 
                        type="primary" 
                        ghost 
                        icon={<FileAddOutlined />}
                        style={{ 
                          borderRadius: '6px',
                          boxShadow: '0 2px 5px rgba(0,0,0,0.05)'
                        }}
                      >
                        上传文档 / Upload Documents
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              messages.map((msg, index) => (
                <MessageItem 
                  key={index} 
                  message={msg.content} 
                  type={msg.type} 
                  model={msg.model}
                  modelType={msg.modelType}
                  usedLocalKnowledge={msg.usedLocalKnowledge}
                  collection={msg.collection}
                />
              ))
            )}
            
            {isLoading && (
              <div style={{ 
                textAlign: 'center', 
                padding: '16px',
                background: 'rgba(255, 255, 255, 0.7)',
                borderRadius: '8px',
                margin: '8px 0'
              }}>
                <Spin size="small" />
                <div style={{ marginTop: '8px', color: '#666' }}>
                  AI 正在思考... / AI is thinking...
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
          
          <Form.Item label="联网查询 / Web Search" style={{ marginBottom: 0 }}>
            <Switch checked={useWebSearch} onChange={setUseWebSearch} />
          </Form.Item>
          
          <Form.Item label="搜索引擎 / Search Engine" style={{ marginBottom: 0 }}>
            <Select value={webSearchEngine} onChange={setWebSearchEngine} style={{ width: 160 }}>
              <Select.Option value="duckduckgo">DuckDuckGo</Select.Option>
              <Select.Option value="bing">Bing</Select.Option>
              <Select.Option value="google">Google</Select.Option>
              <Select.Option value="baidu">Baidu</Select.Option>
            </Select>
          </Form.Item>
          
          <ChatInput
            onSendMessage={handleSendMessage}
            collections={collections}
            models={models}
            modelType={modelType}
            setModelType={setModelType}
            isLoading={isLoading}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default ChatPage;