import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Select, Typography, message, Tabs, Space, Divider, Alert, Table, Tag } from 'antd';
import { SaveOutlined, ReloadOutlined, CheckCircleOutlined, SyncOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const SettingsPage = () => {
  const [deepseekForm] = Form.useForm();
  const [ollamaForm] = Form.useForm();
  const [openaiForm] = Form.useForm();
  const [claudeForm] = Form.useForm();
  const [geminiForm] = Form.useForm();
  const [siliconflowForm] = Form.useForm();
  const [azureOpenaiForm] = Form.useForm();
  const [xaiForm] = Form.useForm();
  const [perplexityForm] = Form.useForm();
  const [groqForm] = Form.useForm();
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('unknown'); // 'success', 'error', 'unknown'
  const [errorMessage, setErrorMessage] = useState('');
  const [openaiValidateMsg, setOpenaiValidateMsg] = useState('');
  const [claudeValidateMsg, setClaudeValidateMsg] = useState('');
  const [geminiValidateMsg, setGeminiValidateMsg] = useState('');
  const [siliconflowValidateMsg, setSiliconflowValidateMsg] = useState('');
  const [azureOpenaiValidateMsg, setAzureOpenaiValidateMsg] = useState('');
  const [xaiValidateMsg, setXaiValidateMsg] = useState('');
  const [perplexityValidateMsg, setPerplexityValidateMsg] = useState('');
  const [groqValidateMsg, setGroqValidateMsg] = useState('');
  
  // 加载设置和模型列表
  useEffect(() => {
    loadSettings('deepseek');
    loadSettings('ollama');
    loadSettings('openai');
    loadSettings('claude');
    loadSettings('gemini');
    loadSettings('siliconflow');
    loadSettings('azure_openai');
    loadSettings('xai');
    loadSettings('perplexity');
    loadSettings('groq');
    fetchModels();
  }, []);
  
  const fetchModels = async () => {
    setLoading(true);
    setErrorMessage('');
    
    try {
      // Load and apply settings for both Ollama and DeepSeek
      const ollamaSettings = localStorage.getItem('ollamaSettings');
      const parsedOllamaSettings = ollamaSettings ? JSON.parse(ollamaSettings) : {};
      
      const deepseekSettings = localStorage.getItem('deepseekSettings');
      const parsedDeepseekSettings = deepseekSettings ? JSON.parse(deepseekSettings) : {};
      
      const openaiSettings = localStorage.getItem('openaiSettings');
      const parsedOpenaiSettings = openaiSettings ? JSON.parse(openaiSettings) : {};
      
      const claudeSettings = localStorage.getItem('claudeSettings');
      const parsedClaudeSettings = claudeSettings ? JSON.parse(claudeSettings) : {};
      
      const geminiSettings = localStorage.getItem('geminiSettings');
      const parsedGeminiSettings = geminiSettings ? JSON.parse(geminiSettings) : {};
      
      const siliconflowSettings = localStorage.getItem('siliconflowSettings');
      const parsedSiliconflowSettings = siliconflowSettings ? JSON.parse(siliconflowSettings) : {};
      
      const azureOpenaiSettings = localStorage.getItem('azure_openaiSettings');
      const parsedAzureOpenaiSettings = azureOpenaiSettings ? JSON.parse(azureOpenaiSettings) : {};
      
      const xaiSettings = localStorage.getItem('xaiSettings');
      const parsedXaiSettings = xaiSettings ? JSON.parse(xaiSettings) : {};
      
      const perplexitySettings = localStorage.getItem('perplexitySettings');
      const parsedPerplexitySettings = perplexitySettings ? JSON.parse(perplexitySettings) : {};
      
      const groqSettings = localStorage.getItem('groqSettings');
      const parsedGroqSettings = groqSettings ? JSON.parse(groqSettings) : {};
      
      // Configure API settings
      if (parsedOllamaSettings.apiUrl) {
        api.setOllamaApiUrl(parsedOllamaSettings.apiUrl);
      }
      
      if (parsedDeepseekSettings.apiKey && parsedDeepseekSettings.apiUrl) {
        api.setDeepseekConfig(parsedDeepseekSettings.apiKey, parsedDeepseekSettings.apiUrl);
      }
      
      if (parsedOpenaiSettings.apiKey && parsedOpenaiSettings.apiUrl) {
        api.setOpenaiConfig(parsedOpenaiSettings.apiKey, parsedOpenaiSettings.apiUrl);
      }
      
      if (parsedClaudeSettings.apiKey) {
        api.setClaudeConfig(parsedClaudeSettings.apiKey, parsedClaudeSettings.apiUrl);
      }
      
      if (parsedGeminiSettings.apiKey) {
        api.setGeminiConfig(parsedGeminiSettings.apiKey, parsedGeminiSettings.apiUrl);
      }
      
      if (parsedSiliconflowSettings.apiKey) {
        api.setSiliconflowConfig(parsedSiliconflowSettings.apiKey, parsedSiliconflowSettings.apiUrl);
      }
      
      if (parsedAzureOpenaiSettings.apiKey) {
        api.setAzureOpenaiConfig(parsedAzureOpenaiSettings.apiKey, parsedAzureOpenaiSettings.azureEndpoint, parsedAzureOpenaiSettings.deploymentName);
      }
      
      if (parsedXaiSettings.apiKey) {
        api.setXaiConfig(parsedXaiSettings.apiKey, parsedXaiSettings.apiUrl);
      }
      
      if (parsedPerplexitySettings.apiKey) {
        api.setPerplexityConfig(parsedPerplexitySettings.apiKey, parsedPerplexitySettings.apiUrl);
      }
      
      if (parsedGroqSettings.apiKey) {
        api.setGroqConfig(parsedGroqSettings.apiKey, parsedGroqSettings.apiUrl);
      }
      
      const response = await api.getModels();
      
      if (response.data && response.data.ollama_models) {
        setModels(response.data.ollama_models || []);
        setConnectionStatus('success');
      } else {
        throw new Error('无法获取模型列表 / Failed to get model list');
      }
      
      console.log("Claude Models:", response.data.claude_models);
      console.log("Gemini Models:", response.data.gemini_models);
      console.log("SiliconFlow Models:", response.data.siliconflow_models);
      console.log("Azure OpenAI Models:", response.data.azure_openai_models);
      console.log("xAI Models:", response.data.xai_models);
      console.log("Perplexity Models:", response.data.perplexity_models);
      console.log("Groq Models:", response.data.groq_models);
    } catch (error) {
      console.error('Error fetching models:', error);
      setConnectionStatus('error');
      setErrorMessage(error.message || '无法连接到Ollama服务，请检查URL是否正确且Ollama服务是否运行 / Unable to connect to Ollama service, please check if URL is correct and Ollama service is running');
      setModels([]);
    } finally {
      setLoading(false);
    }
  };
  
  const saveSettings = (formType) => {
    if (formType === 'deepseek') {
      const values = deepseekForm.getFieldsValue();
      
      // Basic validation for DeepSeek API key
      if (values.apiKey && !values.apiKey.startsWith('sk-')) {
        message.warning('DeepSeek API密钥格式可能不正确，应以sk-开头 / DeepSeek API key format may be incorrect, should start with sk-');
      }
      
      localStorage.setItem('deepseekSettings', JSON.stringify(values));
      
      // Update DeepSeek API configuration
      if (values.apiKey && values.apiUrl) {
        api.setDeepseekConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('DeepSeek设置已保存成功 / DeepSeek settings saved successfully');
      
      // Also refresh models to pick up any DeepSeek configuration changes
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'ollama') {
      const values = ollamaForm.getFieldsValue();
      localStorage.setItem('ollamaSettings', JSON.stringify(values));
      
      // 更新API URL
      if (values.apiUrl) {
        api.setOllamaApiUrl(values.apiUrl);
      }
      
      message.success('Ollama设置已保存成功 / Ollama settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'openai') {
      const values = openaiForm.getFieldsValue();
      localStorage.setItem('openaiSettings', JSON.stringify(values));
      
      // 更新API URL
      if (values.apiUrl) {
        api.setOpenaiConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('OpenAI设置已保存成功 / OpenAI settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'claude') {
      const values = claudeForm.getFieldsValue();
      localStorage.setItem('claudeSettings', JSON.stringify(values));
      
      // 更新Claude API配置
      if (values.apiKey) {
        api.setClaudeConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('Claude设置已保存成功 / Claude settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'gemini') {
      const values = geminiForm.getFieldsValue();
      localStorage.setItem('geminiSettings', JSON.stringify(values));
      
      // 更新Gemini API配置
      if (values.apiKey) {
        api.setGeminiConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('Gemini设置已保存成功 / Gemini settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'siliconflow') {
      const values = siliconflowForm.getFieldsValue();
      localStorage.setItem('siliconflowSettings', JSON.stringify(values));
      
      // 更新SiliconFlow API配置
      if (values.apiKey) {
        api.setSiliconflowConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('SiliconFlow设置已保存成功 / SiliconFlow settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'azure_openai') {
      const values = azureOpenaiForm.getFieldsValue();
      localStorage.setItem('azure_openaiSettings', JSON.stringify(values));
      
      // 更新Azure OpenAI API配置
      if (values.apiKey && values.azureEndpoint && values.deploymentName) {
        api.setAzureOpenaiConfig(values.apiKey, values.azureEndpoint, values.deploymentName);
      }
      
      message.success('Azure OpenAI设置已保存成功 / Azure OpenAI settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'xai') {
      const values = xaiForm.getFieldsValue();
      localStorage.setItem('xaiSettings', JSON.stringify(values));
      
      // 更新xAI API配置
      if (values.apiKey) {
        api.setXaiConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('xAI设置已保存成功 / xAI settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'perplexity') {
      const values = perplexityForm.getFieldsValue();
      localStorage.setItem('perplexitySettings', JSON.stringify(values));
      
      // 更新Perplexity API配置
      if (values.apiKey) {
        api.setPerplexityConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('Perplexity设置已保存成功 / Perplexity settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    } else if (formType === 'groq') {
      const values = groqForm.getFieldsValue();
      localStorage.setItem('groqSettings', JSON.stringify(values));
      
      // 更新Groq API配置
      if (values.apiKey) {
        api.setGroqConfig(values.apiKey, values.apiUrl);
      }
      
      message.success('Groq设置已保存成功 / Groq settings saved successfully');
      
      // 保存后重新连接
      setTimeout(() => {
        fetchModels();
      }, 500);
    }
  };
  
  const loadSettings = (formType) => {
    if (formType === 'deepseek') {
      const settings = localStorage.getItem('deepseekSettings');
      if (settings) {
        deepseekForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'ollama') {
      const settings = localStorage.getItem('ollamaSettings');
      if (settings) {
        ollamaForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'openai') {
      const settings = localStorage.getItem('openaiSettings');
      if (settings) {
        openaiForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'claude') {
      const settings = localStorage.getItem('claudeSettings');
      if (settings) {
        claudeForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'gemini') {
      const settings = localStorage.getItem('geminiSettings');
      if (settings) {
        geminiForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'siliconflow') {
      const settings = localStorage.getItem('siliconflowSettings');
      if (settings) {
        siliconflowForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'azure_openai') {
      const settings = localStorage.getItem('azure_openaiSettings');
      if (settings) {
        azureOpenaiForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'xai') {
      const settings = localStorage.getItem('xaiSettings');
      if (settings) {
        xaiForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'perplexity') {
      const settings = localStorage.getItem('perplexitySettings');
      if (settings) {
        perplexityForm.setFieldsValue(JSON.parse(settings));
      }
    } else if (formType === 'groq') {
      const settings = localStorage.getItem('groqSettings');
      if (settings) {
        groqForm.setFieldsValue(JSON.parse(settings));
      }
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: '模型名称 / Model Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: '大小 / Size',
      dataIndex: 'size',
      key: 'size',
      render: (size) => <Tag color="blue">{size}</Tag>
    },
    {
      title: '修改时间 / Modified',
      dataIndex: 'modified',
      key: 'modified',
      render: (time) => <Text type="secondary">{time}</Text>
    }
  ];
  
  const handleValidateOpenAI = async () => {
    const values = openaiForm.getFieldsValue();
    if (!values.apiKey || !values.apiUrl) {
        message.warning('请先填写 OpenAI API Key 和 API URL');
        return;
    }
    setOpenaiValidateMsg('验证中...');
    try {
        const res = await api.validateOpenaiApi(values.apiKey, values.apiUrl);
        if (res.data.success) {
            setOpenaiValidateMsg('✅ ' + res.data.message);
            message.success(res.data.message);
        } else {
             setOpenaiValidateMsg('❌ ' + res.data.message);
            message.error(res.data.message);
        }
    } catch (err) {
        setOpenaiValidateMsg('验证请求失败，请检查网络或后端服务');
        message.error('验证请求失败');
    }
  };
  
  const handleValidateClaude = async () => {
    const values = claudeForm.getFieldsValue();
    if (!values.apiKey) {
        message.warning('请先填写 Claude API Key');
        return;
    }
    setClaudeValidateMsg('验证中...');
    try {
        const res = await api.validateClaudeApi(values.apiKey, values.apiUrl);
        if (res.data.success) {
            setClaudeValidateMsg('✅ ' + res.data.message);
            message.success(res.data.message);
        } else {
             setClaudeValidateMsg('❌ ' + res.data.message);
            message.error(res.data.message);
        }
    } catch (err) {
        setClaudeValidateMsg('验证请求失败，请检查网络或后端服务');
        message.error('验证请求失败');
    }
  };
  
  const handleValidateGemini = async () => {
    const values = geminiForm.getFieldsValue();
    if (!values.apiKey) {
        message.warning('请先填写 Google Gemini API Key');
        return;
    }
    setGeminiValidateMsg('验证中...');
    try {
        const res = await api.validateGeminiApi(values.apiKey, values.apiUrl);
        if (res.data.success) {
            setGeminiValidateMsg('✅ ' + res.data.message);
            message.success(res.data.message);
        } else {
             setGeminiValidateMsg('❌ ' + res.data.message);
            message.error(res.data.message);
        }
    } catch (err) {
        setGeminiValidateMsg('验证请求失败，请检查网络或后端服务');
        message.error('验证请求失败');
    }
  };
  
  const handleValidateSiliconflow = async () => {
    const values = siliconflowForm.getFieldsValue();
    if (!values.apiKey) { message.warning('请先填写 SiliconFlow API Key'); return; }
    setSiliconflowValidateMsg('验证中...');
    try {
        const res = await api.validateSiliconflowApi(values.apiKey, values.apiUrl);
        setSiliconflowValidateMsg(res.data.success ? '✅ ' + res.data.message : '❌ ' + res.data.message);
        if(res.data.success) message.success(res.data.message); else message.error(res.data.message);
    } catch (err) { setSiliconflowValidateMsg('验证请求失败'); message.error('验证请求失败'); }
  };
  
  const handleValidateAzureOpenai = async () => {
    const values = azureOpenaiForm.getFieldsValue();
    if (!values.apiKey || !values.azureEndpoint || !values.deploymentName) { message.warning('请填写 Azure Key, Endpoint 和 Deployment'); return; }
    setAzureOpenaiValidateMsg('验证中...');
    try {
        const res = await api.validateAzureOpenaiApi(values.apiKey, values.azureEndpoint, values.deploymentName);
        setAzureOpenaiValidateMsg(res.data.success ? '✅ ' + res.data.message : '❌ ' + res.data.message);
        if(res.data.success) message.success(res.data.message); else message.error(res.data.message);
    } catch (err) { setAzureOpenaiValidateMsg('验证请求失败'); message.error('验证请求失败'); }
  };
  
  const handleValidateXai = async () => {
    const values = xaiForm.getFieldsValue();
    if (!values.apiKey) { message.warning('请先填写 xAI API Key'); return; }
    setXaiValidateMsg('验证中...');
    try {
        const res = await api.validateXaiApi(values.apiKey, values.apiUrl);
        setXaiValidateMsg(res.data.success ? '✅ ' + res.data.message : '❌ ' + res.data.message);
        if(res.data.success) message.success(res.data.message); else message.error(res.data.message);
    } catch (err) { setXaiValidateMsg('验证请求失败'); message.error('验证请求失败'); }
  };
  
  const handleValidatePerplexity = async () => {
    const values = perplexityForm.getFieldsValue();
    if (!values.apiKey) { message.warning('请先填写 Perplexity API Key'); return; }
    setPerplexityValidateMsg('验证中...');
    try {
        const res = await api.validatePerplexityApi(values.apiKey, values.apiUrl);
        setPerplexityValidateMsg(res.data.success ? '✅ ' + res.data.message : '❌ ' + res.data.message);
        if(res.data.success) message.success(res.data.message); else message.error(res.data.message);
    } catch (err) { setPerplexityValidateMsg('验证请求失败'); message.error('验证请求失败'); }
  };
  
  const handleValidateGroq = async () => {
    const values = groqForm.getFieldsValue();
    if (!values.apiKey) { message.warning('请先填写 Groq API Key'); return; }
    setGroqValidateMsg('验证中...');
    try {
        const res = await api.validateGroqApi(values.apiKey, values.apiUrl);
        setGroqValidateMsg(res.data.success ? '✅ ' + res.data.message : '❌ ' + res.data.message);
        if(res.data.success) message.success(res.data.message); else message.error(res.data.message);
    } catch (err) { setGroqValidateMsg('验证请求失败'); message.error('验证请求失败'); }
  };
  
  return (
    <Card style={{ margin: '24px 0' }}>
      <Title level={4}>设置 / Settings</Title>
      
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: 'DeepSeek API 设置',
            children: (
              <Form
                form={deepseekForm}
                layout="vertical"
                initialValues={{
                  apiUrl: 'https://api.deepseek.com/v1',
                  defaultModel: 'deepseek-chat',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="DeepSeek API Key"
                  rules={[{ required: true, message: '请输入您的DeepSeek API Key / Please enter your DeepSeek API Key' }]}
                >
                  <Input.Password placeholder="输入您的DeepSeek API Key / Enter your DeepSeek API Key" />
                </Form.Item>
                
                <Form.Item
                  name="apiUrl"
                  label="DeepSeek API URL"
                  rules={[{ required: true, message: '请输入DeepSeek API URL / Please enter DeepSeek API URL' }]}
                >
                  <Input placeholder="输入DeepSeek API URL / Enter DeepSeek API URL" />
                </Form.Item>
                
                <Form.Item
                  name="defaultModel"
                  label="默认模型 / Default Model"
                >
                  <Input placeholder="默认使用的模型 / Default model to use" />
                </Form.Item>
                
                <Space>
                  <Button 
                    type="primary" 
                    icon={<SaveOutlined />} 
                    onClick={() => saveSettings('deepseek')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />} 
                    onClick={() => loadSettings('deepseek')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button
                    onClick={async () => {
                      const values = deepseekForm.getFieldsValue();
                      if (!values.apiKey || !values.apiUrl) {
                        message.warning('请先填写API Key和API URL');
                        return;
                      }
                      try {
                        const res = await api.validateDeepseekApi(values.apiKey, values.apiUrl);
                        if (res.data.success) {
                          message.success(res.data.message);
                        } else {
                          message.error(res.data.message);
                        }
                      } catch (err) {
                        message.error('验证请求失败');
                      }
                    }}
                  >
                    验证API / Validate API
                  </Button>
                </Space>
              </Form>
            ),
          },
          {
            key: '2',
            label: 'Ollama 设置',
            children: (
              <Form
                form={ollamaForm}
                layout="vertical"
                initialValues={{
                  apiUrl: 'http://localhost:11434/api',
                  defaultModel: 'gemma3:4b',
                }}
              >
                <Form.Item
                  name="apiUrl"
                  label="Ollama API URL"
                  rules={[{ required: true, message: '请输入Ollama API URL / Please enter Ollama API URL' }]}
                  extra="默认为http://localhost:11434/api，除非更改了Ollama配置"
                >
                  <Input placeholder="输入Ollama API URL / Enter Ollama API URL" />
                </Form.Item>
                
                <Form.Item
                  name="defaultModel"
                  label="默认模型 / Default Model"
                >
                  <Input placeholder="默认使用的模型 / Default model to use" />
                </Form.Item>
                
                <Space>
                  <Button 
                    type="primary" 
                    icon={<SaveOutlined />} 
                    onClick={() => saveSettings('ollama')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  
                  <Button 
                    icon={<ReloadOutlined />} 
                    onClick={() => loadSettings('ollama')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  
                  <Button 
                    icon={<SyncOutlined />} 
                    onClick={fetchModels}
                    loading={loading}
                  >
                    刷新模型列表 / Refresh Models
                  </Button>
                </Space>
                
                <Divider style={{ margin: '16px 0' }} />
                
                {connectionStatus === 'success' && (
                  <Alert 
                    message="连接成功 / Connection Successful" 
                    description="已成功连接到Ollama服务 / Successfully connected to Ollama service" 
                    type="success" 
                    showIcon 
                    icon={<CheckCircleOutlined />}
                    style={{ marginBottom: '16px' }}
                  />
                )}
                
                {connectionStatus === 'error' && (
                  <Alert 
                    message="连接失败 / Connection Failed" 
                    description={errorMessage} 
                    type="error" 
                    showIcon 
                    style={{ marginBottom: '16px' }}
                  />
                )}
                
                <Title level={5}>可用模型 / Available Models (Ollama)</Title>
                <Table 
                  dataSource={models}
                  columns={columns}
                  rowKey="name"
                  loading={loading}
                  pagination={false}
                  locale={{ emptyText: '无可用Ollama模型 / No Ollama models available' }}
                  style={{ marginBottom: '24px' }}
                />
                
                <Paragraph type="secondary">
                  如果您的Ollama模型未列出，请确保Ollama服务正在运行，并且正确安装了模型。
                  <br />
                  If your Ollama models are not listed, make sure Ollama service is running and the models are correctly installed.
                </Paragraph>
              </Form>
            ),
          },
          {
            key: '3',
            label: 'OpenAI API 设置',
            children: (
              <Form
                form={openaiForm}
                layout="vertical"
                initialValues={{
                  apiUrl: 'https://api.openai.com/v1',
                  defaultModel: 'gpt-3.5-turbo',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="OpenAI API Key"
                  rules={[{ required: true, message: '请输入您的 OpenAI API Key' }]}
                >
                  <Input.Password placeholder="输入您的 OpenAI API Key" />
                </Form.Item>

                <Form.Item
                  name="apiUrl"
                  label="OpenAI API URL"
                  rules={[{ required: true, message: '请输入 OpenAI API URL' }]}
                  extra="默认为 https://api.openai.com/v1"
                >
                  <Input placeholder="输入 OpenAI API URL" />
                </Form.Item>

                <Form.Item
                  name="defaultModel"
                  label="默认模型 / Default Model"
                  rules={[{ required: true, message: '请输入默认使用的 OpenAI 模型' }]}
                >
                  <Input placeholder="例如：gpt-3.5-turbo, gpt-4" />
                </Form.Item>

                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('openai')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('openai')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button onClick={handleValidateOpenAI}>
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: openaiValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {openaiValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '4',
            label: 'Claude API 设置',
            children: (
              <Form
                form={claudeForm}
                layout="vertical"
                initialValues={{
                  apiUrl: 'https://api.anthropic.com/v1',
                  defaultModel: 'claude-3-5-sonnet-20240620',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="Claude API Key"
                  rules={[{ required: true, message: '请输入您的 Claude API Key' }]}
                >
                  <Input.Password placeholder="输入您的 Claude API Key (通常以 sk-ant- 开头)" />
                </Form.Item>

                <Form.Item
                  name="apiUrl"
                  label="Claude API URL"
                  rules={[{ required: true, message: '请输入 Claude API URL' }]}
                  extra="默认为 https://api.anthropic.com/v1"
                >
                  <Input placeholder="输入 Claude API URL" />
                </Form.Item>

                <Form.Item
                  name="defaultModel"
                  label="默认模型 / Default Model"
                  rules={[{ required: true, message: '请输入默认使用的 Claude 模型' }]}
                >
                  <Input placeholder="例如：claude-3-5-sonnet-20240620" />
                </Form.Item>

                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('claude')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('claude')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button onClick={handleValidateClaude}>
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: claudeValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {claudeValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '5',
            label: 'Google Gemini API 设置',
            children: (
              <Form
                form={geminiForm}
                layout="vertical"
                initialValues={{
                  apiUrl: 'https://generativelanguage.googleapis.com',
                  defaultModel: 'gemini-1.5-pro-latest',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="Google Gemini API Key"
                  rules={[{ required: true, message: '请输入您的 Google Gemini API Key' }]}
                >
                  <Input.Password placeholder="输入您的 Google Gemini API Key" />
                </Form.Item>

                <Form.Item
                  name="apiUrl"
                  label="Google Gemini API URL"
                  rules={[{ required: true, message: '请输入 Google Gemini API URL' }]}
                  extra="默认为 https://generativelanguage.googleapis.com"
                >
                  <Input placeholder="输入 Google Gemini API URL" />
                </Form.Item>

                <Form.Item
                  name="defaultModel"
                  label="默认模型 / Default Model"
                  rules={[{ required: true, message: '请输入默认使用的 Google Gemini 模型' }]}
                >
                  <Input placeholder="例如：gemini-1.5-pro-latest" />
                </Form.Item>

                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('gemini')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('gemini')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button onClick={handleValidateGemini}>
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: geminiValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {geminiValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '6',
            label: 'SiliconFlow API 设置',
            children: (
              <Form
                form={siliconflowForm}
                layout="vertical"
                initialValues={{
                  apiUrl: 'https://api.siliconflow.cn',
                  defaultModel: 'Qwen/Qwen2-7B-Instruct',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="SiliconFlow API Key"
                  rules={[{ required: true, message: '请输入 API Key' }]}
                >
                  <Input.Password placeholder="输入 SiliconFlow API Key" />
                </Form.Item>
                <Form.Item
                  name="apiUrl"
                  label="SiliconFlow API URL"
                  rules={[{ required: true, message: '请输入 API URL' }]}
                >
                  <Input placeholder="默认: https://api.siliconflow.cn" />
                </Form.Item>
                <Form.Item
                  name="defaultModel"
                  label="默认模型"
                >
                  <Input placeholder="例如: Qwen/Qwen2-7B-Instruct" />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('siliconflow')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('siliconflow')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button
                    onClick={handleValidateSiliconflow}
                  >
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: siliconflowValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {siliconflowValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '7',
            label: 'Azure OpenAI API 设置',
            children: (
              <Form
                form={azureOpenaiForm}
                layout="vertical"
                initialValues={{
                  apiUrl: '',
                  deploymentName: '',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="Azure OpenAI API Key"
                  rules={[{ required: true, message: '请输入 Key' }]}
                >
                  <Input.Password placeholder="输入 Azure OpenAI Key" />
                </Form.Item>
                <Form.Item
                  name="azureEndpoint"
                  label="Azure Endpoint URL"
                  rules={[{ required: true, message: '请输入 Endpoint URL' }]}
                >
                  <Input placeholder="例如: https://your-resource.openai.azure.com/" />
                </Form.Item>
                <Form.Item
                  name="deploymentName"
                  label="Deployment Name"
                  rules={[{ required: true, message: '请输入 Deployment Name' }]}
                >
                  <Input placeholder="输入 Azure 门户中的模型部署名称" />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('azure_openai')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('azure_openai')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button
                    onClick={handleValidateAzureOpenai}
                  >
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: azureOpenaiValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {azureOpenaiValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '8',
            label: 'xAI API 设置',
            children: (
              <Form
                form={xaiForm}
                layout="vertical"
                initialValues={{
                  apiUrl: '',
                  defaultModel: 'grok-1',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="xAI API Key"
                  rules={[{ required: true, message: '请输入 API Key' }]}
                >
                  <Input.Password placeholder="输入 xAI API Key" />
                </Form.Item>
                <Form.Item
                  name="defaultModel"
                  label="默认模型"
                >
                  <Input placeholder="例如: grok-1" />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('xai')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('xai')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button
                    onClick={handleValidateXai}
                  >
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: xaiValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {xaiValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '9',
            label: 'Perplexity API 设置',
            children: (
              <Form
                form={perplexityForm}
                layout="vertical"
                initialValues={{
                  apiUrl: '',
                  defaultModel: 'llama-3-sonar-large-32k-chat',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="Perplexity API Key"
                  rules={[{ required: true, message: '请输入 API Key' }]}
                >
                  <Input.Password placeholder="输入 Perplexity API Key (通常以 pplx- 开头)" />
                </Form.Item>
                <Form.Item
                  name="defaultModel"
                  label="默认模型"
                >
                  <Input placeholder="例如: llama-3-sonar-large-32k-chat" />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('perplexity')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('perplexity')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button
                    onClick={handleValidatePerplexity}
                  >
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: perplexityValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {perplexityValidateMsg}
                </div>
              </Form>
            ),
          },
          {
            key: '10',
            label: 'Groq API 设置',
            children: (
              <Form
                form={groqForm}
                layout="vertical"
                initialValues={{
                  apiUrl: '',
                  defaultModel: 'llama3-70b-8192',
                }}
              >
                <Form.Item
                  name="apiKey"
                  label="Groq API Key"
                  rules={[{ required: true, message: '请输入 API Key' }]}
                >
                  <Input.Password placeholder="输入 Groq API Key (通常以 gsk_ 开头)" />
                </Form.Item>
                <Form.Item
                  name="defaultModel"
                  label="默认模型"
                >
                  <Input placeholder="例如: llama3-70b-8192" />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => saveSettings('groq')}
                  >
                    保存设置 / Save Settings
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => loadSettings('groq')}
                  >
                    重置为已保存 / Reset to Saved
                  </Button>
                  <Button
                    onClick={handleValidateGroq}
                  >
                    验证API / Validate API
                  </Button>
                </Space>
                <div style={{ marginTop: 16, color: groqValidateMsg.startsWith('❌') ? 'red' : 'green' }}>
                    {groqValidateMsg}
                </div>
              </Form>
            ),
          },
        ]}
      />
      
      <Divider />
      
      <div>
        <Title level={5}>关于 / About</Title>
        <Paragraph>
          AI-DeepSeek是一个个性化AI代理平台，让您可以与多种大语言模型API（包括DeepSeek, Ollama, OpenAI, Claude, Gemini, SiliconFlow, Azure OpenAI, xAI, Perplexity, Groq等）交互。
          上传您的专业文档以创建知识增强的AI响应，适合您的特定领域。
          <br /><br />
          AI-DeepSeek is a personalized AI agent platform that allows you to interact with
          various Large Language Model APIs (including DeepSeek, local Ollama, OpenAI, Claude, Google Gemini, SiliconFlow, Azure OpenAI, xAI, Perplexity, Groq, etc.). Upload your professional documents to create
          knowledge-enhanced AI responses tailored to your specific domain.
        </Paragraph>
        <Paragraph>
          此应用程序为演示如何将大型语言模型与本地知识库集成而构建，创建个性化AI代理。
          <br />
          This application was built as a demonstration of integrating large language models
          with local knowledge bases to create personalized AI agents.
        </Paragraph>
      </div>
    </Card>
  );
};

export default SettingsPage;