import httpx
from bs4 import BeautifulSoup

async def duckduckgo_search(query, max_results=3, timeout=3):
    url = f"https://html.duckduckgo.com/html/?q={query}"
    headers = {"User-Agent": "Mozilla/5.0"}
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url, headers=headers)
        soup = BeautifulSoup(resp.text, "html.parser")
        results = []
        for a in soup.select('.result__snippet')[:max_results]:
            text = a.get_text(strip=True)
            if text:
                results.append(text)
        return results
    except Exception as e:
        print(f"DuckDuckGo search error: {e}")
        return []

async def bing_search(query, max_results=3, api_key=None, timeout=3):
    url = "https://api.bing.microsoft.com/v7.0/search"
    headers = {"Ocp-Apim-Subscription-Key": api_key}
    params = {"q": query, "count": max_results}
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url, headers=headers, params=params)
        data = resp.json()
        return [item["snippet"] for item in data.get("webPages", {}).get("value", [])]
    except Exception as e:
        print(f"Bing search error: {e}")
        return []

async def google_search(query, max_results=3, api_key=None, cx=None, timeout=3):
    url = "https://www.googleapis.com/customsearch/v1"
    params = {"q": query, "key": api_key, "cx": cx, "num": max_results}
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url, params=params)
        data = resp.json()
        return [item["snippet"] for item in data.get("items", [])]
    except Exception as e:
        print(f"Google search error: {e}")
        return []

async def baidu_search(query, max_results=3, timeout=3):
    url = f"https://www.baidu.com/s?wd={query}"
    headers = {"User-Agent": "Mozilla/5.0"}
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url, headers=headers)
        soup = BeautifulSoup(resp.text, "html.parser")
        results = []
        for div in soup.select('.c-abstract')[:max_results]:
            text = div.get_text(strip=True)
            if text:
                results.append(text)
        return results
    except Exception as e:
        print(f"Baidu search error: {e}")
        return [] 