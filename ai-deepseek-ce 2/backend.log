INFO:     Will watch for changes in these directories: ['/Users/<USER>/my_project/ai-deepseek-ce/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [18204] using StatReload
All required dependencies are installed.
ClaudeClient initialized (API Key: None, URL: https://api.anthropic.com/v1)
GeminiClient initialized (API Key: None, URL: https://generativelanguage.googleapis.com)
SiliconFlowClient initialized (API Key: None, URL: https://api.siliconflow.cn)
AzureOpenAIClient initialized (Endpoint: None, Deployment: None, Key: None)
XAIClient initialized (API Key: None, URL: https://api.x.ai)
PerplexityClient initialized (API Key: None, URL: https://api.perplexity.ai)
GroqClient initialized (API Key: None, URL: https://api.groq.com/openai/v1)
Hostname resolution: 'localhost' resolves correctly
Attempting to initialize Ollama Embeddings (nomic-embed-text)...
Successfully initialized Ollama Embeddings.
INFO:     Started server process [18211]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
All required dependencies are installed.
ClaudeClient initialized (API Key: None, URL: https://api.anthropic.com/v1)
GeminiClient initialized (API Key: None, URL: https://generativelanguage.googleapis.com)
SiliconFlowClient initialized (API Key: None, URL: https://api.siliconflow.cn)
AzureOpenAIClient initialized (Endpoint: None, Deployment: None, Key: None)
XAIClient initialized (API Key: None, URL: https://api.x.ai)
PerplexityClient initialized (API Key: None, URL: https://api.perplexity.ai)
GroqClient initialized (API Key: None, URL: https://api.groq.com/openai/v1)
Hostname resolution: 'localhost' resolves correctly
Attempting to initialize Ollama Embeddings (nomic-embed-text)...
Successfully initialized Ollama Embeddings.
All required dependencies are installed.
ClaudeClient initialized (API Key: None, URL: https://api.anthropic.com/v1)
GeminiClient initialized (API Key: None, URL: https://generativelanguage.googleapis.com)
SiliconFlowClient initialized (API Key: None, URL: https://api.siliconflow.cn)
AzureOpenAIClient initialized (Endpoint: None, Deployment: None, Key: None)
XAIClient initialized (API Key: None, URL: https://api.x.ai)
PerplexityClient initialized (API Key: None, URL: https://api.perplexity.ai)
GroqClient initialized (API Key: None, URL: https://api.groq.com/openai/v1)
Hostname resolution: 'localhost' resolves correctly
Attempting to initialize Ollama Embeddings (nomic-embed-text)...
Successfully initialized Ollama Embeddings.
INFO:     127.0.0.1:57765 - "GET /collections HTTP/1.1" 200 OK
Fetching DeepSeek models from https://api.deepseek.com/v1/models
INFO:     127.0.0.1:57765 - "GET /collections HTTP/1.1" 200 OK
INFO:     None:0 - "GET /manifest.json HTTP/1.1" 404 Not Found
DeepSeek API models response status: 200
DeepSeek API models data: {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}...
Ollama API response: {'models': [{'name': 'gemma3:4b', 'model': 'gemma3:4b', 'modified_at': '2025-03-14T18:38:11.554943206+08:00', 'size': 3338801718, 'digest': 'c0494fe00251c4fc844e6a1801f9cbd26c37441d034af3cb9284402f7e91989d', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'gemma3', 'families': ['gemma3'], 'parameter_size': '4.3B', 'quantization_level': 'Q4_K_M'}}, {'name': 'huihui_ai/deepseek-r1-abliterated:8b', 'model': 'huihui_ai/deepseek-r1-abliterated:8b', 'modified_at': '2025-02-16T09:55:16.666891448+08:00', 'size': 4920738855, 'digest': 'f72bcec0a6da9c42bfa2c342f0e0bfcca3b896b67e75403e13a31c0b9787be75', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'llama', 'families': ['llama'], 'parameter_size': '8.0B', 'quantization_level': 'Q4_K_M'}}, {'name': 'nomic-embed-text:latest', 'model': 'nomic-embed-text:latest', 'modified_at': '2025-01-31T06:04:15.911398784+08:00', 'size': 274302450, 'digest': '0a109f422b47e3a30ba2b10eca18548e944e8a23073ee3f3e947efcf3c45e59f', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'nomic-bert', 'families': ['nomic-bert'], 'parameter_size': '137M', 'quantization_level': 'F16'}}]}
Info: OpenAI API Key not provided, skipping model fetch.
Info: Claude API Key not provided, skipping model fetch.
Info: Gemini API Key not provided, skipping model fetch.
Info: SiliconFlow API Key not provided, skipping model fetch.
Info: Azure OpenAI Key, Endpoint or Deployment Name not provided, skipping model fetch.
Info: xAI API Key not provided, skipping model fetch.
Info: Perplexity API Key not provided, skipping model fetch.
Info: Groq API Key not provided, skipping model fetch.
INFO:     127.0.0.1:57767 - "GET /models?ollamaApiUrl=http:%2F%2Flocalhost:11434%2Fapi&deepseekApiKey=***********************************&deepseekApiUrl=https:%2F%2Fapi.deepseek.com%2Fv1 HTTP/1.1" 200 OK
Fetching DeepSeek models from https://api.deepseek.com/v1/models
DeepSeek API models response status: 200
DeepSeek API models data: {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}...
Ollama API response: {'models': [{'name': 'gemma3:4b', 'model': 'gemma3:4b', 'modified_at': '2025-03-14T18:38:11.554943206+08:00', 'size': 3338801718, 'digest': 'c0494fe00251c4fc844e6a1801f9cbd26c37441d034af3cb9284402f7e91989d', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'gemma3', 'families': ['gemma3'], 'parameter_size': '4.3B', 'quantization_level': 'Q4_K_M'}}, {'name': 'huihui_ai/deepseek-r1-abliterated:8b', 'model': 'huihui_ai/deepseek-r1-abliterated:8b', 'modified_at': '2025-02-16T09:55:16.666891448+08:00', 'size': 4920738855, 'digest': 'f72bcec0a6da9c42bfa2c342f0e0bfcca3b896b67e75403e13a31c0b9787be75', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'llama', 'families': ['llama'], 'parameter_size': '8.0B', 'quantization_level': 'Q4_K_M'}}, {'name': 'nomic-embed-text:latest', 'model': 'nomic-embed-text:latest', 'modified_at': '2025-01-31T06:04:15.911398784+08:00', 'size': 274302450, 'digest': '0a109f422b47e3a30ba2b10eca18548e944e8a23073ee3f3e947efcf3c45e59f', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'nomic-bert', 'families': ['nomic-bert'], 'parameter_size': '137M', 'quantization_level': 'F16'}}]}
Info: OpenAI API Key not provided, skipping model fetch.
Info: Claude API Key not provided, skipping model fetch.
Info: Gemini API Key not provided, skipping model fetch.
Info: SiliconFlow API Key not provided, skipping model fetch.
Info: Azure OpenAI Key, Endpoint or Deployment Name not provided, skipping model fetch.
Info: xAI API Key not provided, skipping model fetch.
Info: Perplexity API Key not provided, skipping model fetch.
Info: Groq API Key not provided, skipping model fetch.
INFO:     127.0.0.1:57794 - "GET /models?ollamaApiUrl=http:%2F%2Flocalhost:11434%2Fapi&deepseekApiKey=***********************************&deepseekApiUrl=https:%2F%2Fapi.deepseek.com%2Fv1 HTTP/1.1" 200 OK
Fetching DeepSeek models from https://api.deepseek.com/v1/models
DeepSeek API models response status: 200
DeepSeek API models data: {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}...
Ollama API response: {'models': [{'name': 'gemma3:4b', 'model': 'gemma3:4b', 'modified_at': '2025-03-14T18:38:11.554943206+08:00', 'size': 3338801718, 'digest': 'c0494fe00251c4fc844e6a1801f9cbd26c37441d034af3cb9284402f7e91989d', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'gemma3', 'families': ['gemma3'], 'parameter_size': '4.3B', 'quantization_level': 'Q4_K_M'}}, {'name': 'huihui_ai/deepseek-r1-abliterated:8b', 'model': 'huihui_ai/deepseek-r1-abliterated:8b', 'modified_at': '2025-02-16T09:55:16.666891448+08:00', 'size': 4920738855, 'digest': 'f72bcec0a6da9c42bfa2c342f0e0bfcca3b896b67e75403e13a31c0b9787be75', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'llama', 'families': ['llama'], 'parameter_size': '8.0B', 'quantization_level': 'Q4_K_M'}}, {'name': 'nomic-embed-text:latest', 'model': 'nomic-embed-text:latest', 'modified_at': '2025-01-31T06:04:15.911398784+08:00', 'size': 274302450, 'digest': '0a109f422b47e3a30ba2b10eca18548e944e8a23073ee3f3e947efcf3c45e59f', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'nomic-bert', 'families': ['nomic-bert'], 'parameter_size': '137M', 'quantization_level': 'F16'}}]}
Info: OpenAI API Key not provided, skipping model fetch.
Info: Claude API Key not provided, skipping model fetch.
Info: Gemini API Key not provided, skipping model fetch.
Info: SiliconFlow API Key not provided, skipping model fetch.
Info: Azure OpenAI Key, Endpoint or Deployment Name not provided, skipping model fetch.
Info: xAI API Key not provided, skipping model fetch.
Info: Perplexity API Key not provided, skipping model fetch.
Info: Groq API Key not provided, skipping model fetch.
INFO:     127.0.0.1:57811 - "GET /models?ollamaApiUrl=http:%2F%2Flocalhost:11434%2Fapi&deepseekApiKey=***********************************&deepseekApiUrl=https:%2F%2Fapi.deepseek.com%2Fv1 HTTP/1.1" 200 OK
Fetching DeepSeek models from https://api.deepseek.com/v1/models
DeepSeek API models response status: 200
DeepSeek API models data: {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}...
Ollama API response: {'models': [{'name': 'gemma3:4b', 'model': 'gemma3:4b', 'modified_at': '2025-03-14T18:38:11.554943206+08:00', 'size': 3338801718, 'digest': 'c0494fe00251c4fc844e6a1801f9cbd26c37441d034af3cb9284402f7e91989d', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'gemma3', 'families': ['gemma3'], 'parameter_size': '4.3B', 'quantization_level': 'Q4_K_M'}}, {'name': 'huihui_ai/deepseek-r1-abliterated:8b', 'model': 'huihui_ai/deepseek-r1-abliterated:8b', 'modified_at': '2025-02-16T09:55:16.666891448+08:00', 'size': 4920738855, 'digest': 'f72bcec0a6da9c42bfa2c342f0e0bfcca3b896b67e75403e13a31c0b9787be75', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'llama', 'families': ['llama'], 'parameter_size': '8.0B', 'quantization_level': 'Q4_K_M'}}, {'name': 'nomic-embed-text:latest', 'model': 'nomic-embed-text:latest', 'modified_at': '2025-01-31T06:04:15.911398784+08:00', 'size': 274302450, 'digest': '0a109f422b47e3a30ba2b10eca18548e944e8a23073ee3f3e947efcf3c45e59f', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'nomic-bert', 'families': ['nomic-bert'], 'parameter_size': '137M', 'quantization_level': 'F16'}}]}
Info: OpenAI API Key not provided, skipping model fetch.
Info: Claude API Key not provided, skipping model fetch.
Info: Gemini API Key not provided, skipping model fetch.
Info: SiliconFlow API Key not provided, skipping model fetch.
Info: Azure OpenAI Key, Endpoint or Deployment Name not provided, skipping model fetch.
Info: xAI API Key not provided, skipping model fetch.
Info: Perplexity API Key not provided, skipping model fetch.
Info: Groq API Key not provided, skipping model fetch.
INFO:     127.0.0.1:57794 - "GET /models?ollamaApiUrl=http:%2F%2Flocalhost:11434%2Fapi&deepseekApiKey=***********************************&deepseekApiUrl=https:%2F%2Fapi.deepseek.com%2Fv1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:57794 - "POST /chat/deepseek HTTP/1.1" 200 OK
Sending request to DeepSeek API (Stream=True): https://api.deepseek.com/v1/chat/completions
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"role":"assistant","content":""},"logprobs":null,"finish_reason":null}]}
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"你好"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 你好
DeepSeek stream line: 你好
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"！"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ！
DeepSeek stream line: ！
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"很高兴"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 很高兴
DeepSeek stream line: 很高兴
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"见到"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 见到
DeepSeek stream line: 见到
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"你"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 你
DeepSeek stream line: 你
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"，"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ，
DeepSeek stream line: ，
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"有什么"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 有什么
DeepSeek stream line: 有什么
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"可以"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 可以
DeepSeek stream line: 可以
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"帮"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 帮
DeepSeek stream line: 帮
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"你的"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 你的
DeepSeek stream line: 你的
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"吗"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 吗
DeepSeek stream line: 吗
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"？"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ？
DeepSeek stream line: ？
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"😊"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 😊
DeepSeek stream line: 😊
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"a0460414-050e-4809-acc7-56e017f6f568","object":"chat.completion.chunk","created":1745111102,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":""},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":26,"completion_tokens":14,"total_tokens":40,"prompt_tokens_details":{"cached_tokens":0},"prompt_cache_hit_tokens":0,"prompt_cache_miss_tokens":26}}
DeepSeek raw stream line: 
DeepSeek raw stream line: data: [DONE]
INFO:     127.0.0.1:57881 - "POST /chat/deepseek HTTP/1.1" 200 OK
Sending request to DeepSeek API (Stream=True): https://api.deepseek.com/v1/chat/completions
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"role":"assistant","content":""},"logprobs":null,"finish_reason":null}]}
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"我可以"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 我可以
DeepSeek stream line: 我可以
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"使用"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 使用
DeepSeek stream line: 使用
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"多种"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 多种
DeepSeek stream line: 多种
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语言"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语言
DeepSeek stream line: 语言
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"进行"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 进行
DeepSeek stream line: 进行
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"交流"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 交流
DeepSeek stream line: 交流
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"，"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ，
DeepSeek stream line: ，
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"包括"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 包括
DeepSeek stream line: 包括
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"但不"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 但不
DeepSeek stream line: 但不
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"限于"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 限于
DeepSeek stream line: 限于
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"以下"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 以下
DeepSeek stream line: 以下
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语言"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语言
DeepSeek stream line: 语言
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"："},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ：
DeepSeek stream line: ：
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n\n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   


DeepSeek stream line:   


DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"中文"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 中文
DeepSeek stream line: 中文
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"简体"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 简体
DeepSeek stream line: 简体
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"/"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: /
DeepSeek stream line: /
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"繁体"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 繁体
DeepSeek stream line: 繁体
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"English"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: English
DeepSeek stream line: English
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"英语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 英语
DeepSeek stream line: 英语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Esp"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Esp
DeepSeek stream line: Esp
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"añ"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: añ
DeepSeek stream line: añ
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ol"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ol
DeepSeek stream line: ol
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"西班牙"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 西班牙
DeepSeek stream line: 西班牙
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Fran"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Fran
DeepSeek stream line: Fran
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"çais"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: çais
DeepSeek stream line: çais
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"法语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 法语
DeepSeek stream line: 法语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"De"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: De
DeepSeek stream line: De
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"utsch"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: utsch
DeepSeek stream line: utsch
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"德语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 德语
DeepSeek stream line: 德语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"日本語"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 日本語
DeepSeek stream line: 日本語
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"日语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 日语
DeepSeek stream line: 日语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"한"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 한
DeepSeek stream line: 한
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"국"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 국
DeepSeek stream line: 국
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"어"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 어
DeepSeek stream line: 어
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"韩"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 韩
DeepSeek stream line: 韩
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Port"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Port
DeepSeek stream line: Port
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ugu"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ugu
DeepSeek stream line: ugu
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ês"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ês
DeepSeek stream line: ês
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"葡萄牙"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 葡萄牙
DeepSeek stream line: 葡萄牙
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"It"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: It
DeepSeek stream line: It
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"al"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: al
DeepSeek stream line: al
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"iano"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: iano
DeepSeek stream line: iano
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"意大利"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 意大利
DeepSeek stream line: 意大利
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Р"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Р
DeepSeek stream line: Р
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ус"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ус
DeepSeek stream line: ус
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ский"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ский
DeepSeek stream line: ский
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"俄"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 俄
DeepSeek stream line: 俄
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"الع"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: الع
DeepSeek stream line: الع
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ربية"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ربية
DeepSeek stream line: ربية
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"阿拉伯"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 阿拉伯
DeepSeek stream line: 阿拉伯
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ह"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ह
DeepSeek stream line: ह
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"िन्द"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: िन्द
DeepSeek stream line: िन्द
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ी"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ी
DeepSeek stream line: ी
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"印"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 印
DeepSeek stream line: 印
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"地"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 地
DeepSeek stream line: 地
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"N"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: N
DeepSeek stream line: N
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"eder"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: eder
DeepSeek stream line: eder
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"lands"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: lands
DeepSeek stream line: lands
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"荷兰"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 荷兰
DeepSeek stream line: 荷兰
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"T"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: T
DeepSeek stream line: T
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ür"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ür
DeepSeek stream line: ür
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"k"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: k
DeepSeek stream line: k
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ç"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ç
DeepSeek stream line: ç
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"e"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: e
DeepSeek stream line: e
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"土耳其"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 土耳其
DeepSeek stream line: 土耳其
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ไทย"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ไทย
DeepSeek stream line: ไทย
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"泰"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 泰
DeepSeek stream line: 泰
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   

DeepSeek stream line:   

DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"-"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: -
DeepSeek stream line: -
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" **"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  **
DeepSeek stream line:  **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Ti"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Ti
DeepSeek stream line: Ti
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ế"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ế
DeepSeek stream line: ế
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ng"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ng
DeepSeek stream line: ng
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" Vi"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  Vi
DeepSeek stream line:  Vi
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"ệt"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ệt
DeepSeek stream line: ệt
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"越南"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 越南
DeepSeek stream line: 越南
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语
DeepSeek stream line: 语
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"**"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: **
DeepSeek stream line: **
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n\n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   


DeepSeek stream line:   


DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"此外"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 此外
DeepSeek stream line: 此外
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"，"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ，
DeepSeek stream line: ，
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"我"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 我
DeepSeek stream line: 我
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"还能"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 还能
DeepSeek stream line: 还能
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"处理"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 处理
DeepSeek stream line: 处理
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"一些"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 一些
DeepSeek stream line: 一些
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"编程"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 编程
DeepSeek stream line: 编程
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语言"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语言
DeepSeek stream line: 语言
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"如"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 如
DeepSeek stream line: 如
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Python"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Python
DeepSeek stream line: Python
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"、"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 、
DeepSeek stream line: 、
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"Java"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: Java
DeepSeek stream line: Java
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"、"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 、
DeepSeek stream line: 、
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"C"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: C
DeepSeek stream line: C
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"++"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ++
DeepSeek stream line: ++
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"等"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 等
DeepSeek stream line: 等
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）
DeepSeek stream line: ）
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"和"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 和
DeepSeek stream line: 和
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"简单的"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 简单的
DeepSeek stream line: 简单的
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"古文"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 古文
DeepSeek stream line: 古文
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"（"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: （
DeepSeek stream line: （
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"如"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 如
DeepSeek stream line: 如
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"文言"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 文言
DeepSeek stream line: 文言
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"文"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 文
DeepSeek stream line: 文
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"）。"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ）。
DeepSeek stream line: ）。
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"如果你"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 如果你
DeepSeek stream line: 如果你
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"有"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 有
DeepSeek stream line: 有
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"特定的"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 特定的
DeepSeek stream line: 特定的
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语言"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语言
DeepSeek stream line: 语言
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"需求"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 需求
DeepSeek stream line: 需求
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"，"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ，
DeepSeek stream line: ，
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"可以"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 可以
DeepSeek stream line: 可以
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"告诉我"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 告诉我
DeepSeek stream line: 告诉我
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"，"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ，
DeepSeek stream line: ，
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"我会"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 我会
DeepSeek stream line: 我会
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"尽力"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 尽力
DeepSeek stream line: 尽力
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"协助"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 协助
DeepSeek stream line: 协助
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"！"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ！
DeepSeek stream line: ！
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":" 😊"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:  😊
DeepSeek stream line:  😊
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"  \n\n"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content:   


DeepSeek stream line:   


DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"你想"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 你想
DeepSeek stream line: 你想
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"用"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 用
DeepSeek stream line: 用
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"哪种"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 哪种
DeepSeek stream line: 哪种
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"语言"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 语言
DeepSeek stream line: 语言
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"交流"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 交流
DeepSeek stream line: 交流
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"呢"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: 呢
DeepSeek stream line: 呢
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":"？"},"logprobs":null,"finish_reason":null}]}
DeepSeek parsed content: ？
DeepSeek stream line: ？
DeepSeek raw stream line: 
DeepSeek raw stream line: data: {"id":"d04cda29-9284-4296-92b6-9dc2b9376aac","object":"chat.completion.chunk","created":1745111137,"model":"deepseek-chat","system_fingerprint":"fp_3d5141a69a_prod0225","choices":[{"index":0,"delta":{"content":""},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":29,"completion_tokens":230,"total_tokens":259,"prompt_tokens_details":{"cached_tokens":0},"prompt_cache_hit_tokens":0,"prompt_cache_miss_tokens":29}}
DeepSeek raw stream line: 
DeepSeek raw stream line: data: [DONE]
Fetching DeepSeek models from https://api.deepseek.com/v1/models
DeepSeek API models response status: 200
DeepSeek API models data: {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}...
Ollama API response: {'models': [{'name': 'gemma3:4b', 'model': 'gemma3:4b', 'modified_at': '2025-03-14T18:38:11.554943206+08:00', 'size': 3338801718, 'digest': 'c0494fe00251c4fc844e6a1801f9cbd26c37441d034af3cb9284402f7e91989d', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'gemma3', 'families': ['gemma3'], 'parameter_size': '4.3B', 'quantization_level': 'Q4_K_M'}}, {'name': 'huihui_ai/deepseek-r1-abliterated:8b', 'model': 'huihui_ai/deepseek-r1-abliterated:8b', 'modified_at': '2025-02-16T09:55:16.666891448+08:00', 'size': 4920738855, 'digest': 'f72bcec0a6da9c42bfa2c342f0e0bfcca3b896b67e75403e13a31c0b9787be75', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'llama', 'families': ['llama'], 'parameter_size': '8.0B', 'quantization_level': 'Q4_K_M'}}, {'name': 'nomic-embed-text:latest', 'model': 'nomic-embed-text:latest', 'modified_at': '2025-01-31T06:04:15.911398784+08:00', 'size': 274302450, 'digest': '0a109f422b47e3a30ba2b10eca18548e944e8a23073ee3f3e947efcf3c45e59f', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'nomic-bert', 'families': ['nomic-bert'], 'parameter_size': '137M', 'quantization_level': 'F16'}}]}
Info: OpenAI API Key not provided, skipping model fetch.
Info: Claude API Key not provided, skipping model fetch.
Info: Gemini API Key not provided, skipping model fetch.
Info: SiliconFlow API Key not provided, skipping model fetch.
Info: Azure OpenAI Key, Endpoint or Deployment Name not provided, skipping model fetch.
Info: xAI API Key not provided, skipping model fetch.
Info: Perplexity API Key not provided, skipping model fetch.
Info: Groq API Key not provided, skipping model fetch.
INFO:     127.0.0.1:57920 - "GET /models?ollamaApiUrl=http:%2F%2Flocalhost:11434%2Fapi&deepseekApiKey=***********************************&deepseekApiUrl=https:%2F%2Fapi.deepseek.com%2Fv1 HTTP/1.1" 200 OK
Fetching DeepSeek models from https://api.deepseek.com/v1/models
DeepSeek API models response status: 200
DeepSeek API models data: {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}...
Ollama API response: {'models': [{'name': 'gemma3:4b', 'model': 'gemma3:4b', 'modified_at': '2025-03-14T18:38:11.554943206+08:00', 'size': 3338801718, 'digest': 'c0494fe00251c4fc844e6a1801f9cbd26c37441d034af3cb9284402f7e91989d', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'gemma3', 'families': ['gemma3'], 'parameter_size': '4.3B', 'quantization_level': 'Q4_K_M'}}, {'name': 'huihui_ai/deepseek-r1-abliterated:8b', 'model': 'huihui_ai/deepseek-r1-abliterated:8b', 'modified_at': '2025-02-16T09:55:16.666891448+08:00', 'size': 4920738855, 'digest': 'f72bcec0a6da9c42bfa2c342f0e0bfcca3b896b67e75403e13a31c0b9787be75', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'llama', 'families': ['llama'], 'parameter_size': '8.0B', 'quantization_level': 'Q4_K_M'}}, {'name': 'nomic-embed-text:latest', 'model': 'nomic-embed-text:latest', 'modified_at': '2025-01-31T06:04:15.911398784+08:00', 'size': 274302450, 'digest': '0a109f422b47e3a30ba2b10eca18548e944e8a23073ee3f3e947efcf3c45e59f', 'details': {'parent_model': '', 'format': 'gguf', 'family': 'nomic-bert', 'families': ['nomic-bert'], 'parameter_size': '137M', 'quantization_level': 'F16'}}]}
Info: OpenAI API Key not provided, skipping model fetch.
Info: Claude API Key not provided, skipping model fetch.
Info: Gemini API Key not provided, skipping model fetch.
Info: SiliconFlow API Key not provided, skipping model fetch.
Info: Azure OpenAI Key, Endpoint or Deployment Name not provided, skipping model fetch.
Info: xAI API Key not provided, skipping model fetch.
Info: Perplexity API Key not provided, skipping model fetch.
Info: Groq API Key not provided, skipping model fetch.
INFO:     127.0.0.1:57920 - "GET /models?ollamaApiUrl=http:%2F%2Flocalhost:11434%2Fapi&deepseekApiKey=***********************************&deepseekApiUrl=https:%2F%2Fapi.deepseek.com%2Fv1 HTTP/1.1" 200 OK
