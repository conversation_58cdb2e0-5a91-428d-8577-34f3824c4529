name: 功能增强请求
description: 提出一个新功能或改进建议
title: "[Enhancement]: "
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        感谢您提出功能增强建议！我们重视每一条改进意见。
        
  - type: checkboxes
    id: prerequisites
    attributes:
      label: 提交前确认
      description: 在提交此功能请求之前，请确认以下几点
      options:
        - label: 我已经搜索过现有的 issues，确认没有类似的功能请求
          required: true
        - label: 我相信这个功能对大多数用户有价值，而不仅仅对我个人有用
          required: true
          
  - type: textarea
    id: problem
    attributes:
      label: 相关问题
      description: 这个功能请求解决了什么问题？它与现有的痛点有何关联？
      placeholder: 清晰地描述你遇到的问题，例如："我总是感到困扰当我尝试..."
    validations:
      required: true
      
  - type: textarea
    id: solution
    attributes:
      label: 建议的解决方案
      description: 描述你希望看到的解决方案
      placeholder: 描述你想要的功能如何工作
    validations:
      required: true
      
  - type: textarea
    id: alternatives
    attributes:
      label: 替代方案
      description: 描述你考虑过的替代解决方案或功能
      placeholder: 有没有其他方式可以解决这个问题？
    validations:
      required: false
      
  - type: textarea
    id: additional
    attributes:
      label: 附加信息
      description: 任何其他背景或截图信息
    validations:
      required: false
