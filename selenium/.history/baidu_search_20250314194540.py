'''
Author: hgxszhj <EMAIL>
Date: 2025-03-14 19:11:02
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2025-03-14 19:30:07
FilePath: /selenium/baidu_search.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def test_baidu_search():
    # 初始化Firefox浏览器驱动
    from selenium.webdriver.firefox.service import Service
    from webdriver_manager.firefox import GeckoDriverManager
    
    # 使用webdriver_manager自动管理GeckoDriver
    service = Service(GeckoDriverManager().install())
    driver = webdriver.Firefox(service=service)
    
    try:
        # 访问百度首页
        driver.get("https://www.baidu.com")
        
        # 找到搜索输入框并输入搜索内容
        search_input = driver.find_element(By.ID, "kw")
        search_input.send_keys("Selenium自动化测试")
        
        # 点击搜索按钮
        search_button = driver.find_element(By.ID, "su")
        search_button.click()
        
        # 等待搜索结果加载
        wait = WebDriverWait(driver, 10)
        wait.until(EC.presence_of_element_located((By.ID, "content_left")))
        
        # 获取并打印第一条搜索结果
        first_result = driver.find_element(By.XPATH, "//div[@id='content_left']//a[1]")
        print(f"第一条搜索结果: {first_result.text}")
        
        # 截图保存
        driver.save_screenshot("search_result.png")
        
        time.sleep(2)
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        
    finally:
        # 关闭浏览器
        driver.quit()

if __name__ == "__main__":
    test_baidu_search()
