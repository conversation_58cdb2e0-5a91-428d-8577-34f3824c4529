import requests
from pprint import pprint
import urllib3
import os
from tqdm import tqdm
import logging

# 禁用 InsecureRequestWarning 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PDFDownloader:
    def __init__(self, headers, base_url):
        self.headers = headers
        self.base_url = base_url

    def fetch_file_info(self, file_id):
        """获取文件信息，返回文件下载的路径 URL 和文件名。"""
        url = f"{self.base_url}/api/v1/tools-center-v2/file-cloud/preview"
        params = {
            "id": file_id,            "_": input("请输入 _ 值: ")
        }
        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False)
            response.raise_for_status()
            data = response.json()
            if not isinstance(data, dict):
                logger.error("响应格式无效，未能获取文件信息。")
                return None, None
            pprint(data)
            file_url = data.get("url")
            filename = data.get("filename")
            if not file_url or not filename:
                logger.error("文件信息不完整，请检查文件 ID 是否正确。")
                return None, None
            return file_url, filename
        except requests.exceptions.RequestException as e:
            logger.error(f"请求文件信息失败: {e}")
            return None, None

    def download_pdf(self, file_url, output_filename):
        """下载 PDF 文件并保存到指定路径。"""
        full_url = f"{self.base_url}{file_url}"
        print(full_url)
        try:
            response = requests.get(full_url, headers=self.headers, verify=False, stream=True)
            response.raise_for_status()
            total_size = int(response.headers.get('content-length', 0))
            with open(output_filename, "wb") as f, tqdm(
                desc=output_filename,
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as bar:
                for data in response.iter_content(chunk_size=1024):
                    f.write(data)
                    bar.update(len(data))
            logger.info(f"文件已保存为 {output_filename}")
        except requests.exceptions.RequestException as e:
            logger.error(f"PDF 文件下载失败: {e}")


def main():
    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh,zh-CN;q=0.9,en;q=0.8",
        "Authorization": input("请输入 Authorization 值: "),
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Cookie": input("请输入 Cookie 值: "),
        "Referer": "https://kc.zhixueyun.com/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
    }

    base_url = input("请输入基础 URL (例如 https://kc.zhixueyun.com): ")
    if not base_url.startswith("http"):
        logger.error("URL 格式不正确，请输入以 http 或 https 开头的基础 URL。")
        return

    downloader = PDFDownloader(headers, base_url)

    file_id = input("请输入文件的 ID: ")
    if not file_id:
        logger.error("文件 ID 不能为空，请重新输入。")
        return

    file_url, filename = downloader.fetch_file_info(file_id)
    if file_url and filename:
        save_path = input("请输入文件保存的目录（默认为当前目录）: ") or "."
        output_filename = os.path.join(save_path, filename)
        downloader.download_pdf(file_url, output_filename)


if __name__ == "__main__":
    main()
