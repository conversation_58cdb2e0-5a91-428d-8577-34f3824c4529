/* 全局样式 */
:root {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6366f1;
    --accent-color: #38bdf8;
    --light-bg: #f9fafb;
    --dark-text: #1f2937;
    --light-text: #f9fafb;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    --transition-speed: 0.2s;
}

body {
    background-color: var(--light-bg);
    color: var(--dark-text);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    font-size: 15px;
}

/* 卡片样式 */
.card {
    box-shadow: var(--card-shadow);
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: box-shadow var(--transition-speed);
    overflow: hidden;
    background: #ffffff;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
    background-color: var(--primary-color);
    padding: 0.75rem 1rem;
    border-bottom: none;
}

.card-header.bg-primary {
    background-color: var(--primary-color) !important;
}

.card-body {
    padding: 1.25rem;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e5e7eb;
    transition: all var(--transition-speed);
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
    outline: none;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.375rem;
    color: #4b5563;
    font-size: 0.95rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all var(--transition-speed);
    box-shadow: none;
    font-size: 0.95rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border: none;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-hover);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-success {
    background-color: var(--success-color);
    border: none;
}

.btn-success:hover {
    background-color: #0ea271;
}

.btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* 导航栏样式 */
.navbar {
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--card-shadow);
    padding: 0.5rem 1rem;
}

.navbar-dark.bg-primary {
    background-color: var(--primary-color) !important;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.1rem;
}

/* 标签页样式 */
.nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    transition: all var(--transition-speed);
    border-radius: 0.375rem;
    margin: 0 0.125rem;
}

.nav-link:hover, .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
}

/* 提示框样式 */
.toast {
    background-color: white;
    border-radius: 0.375rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 模态框样式 */
.modal-content {
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
    padding: 1rem 1.25rem;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.25rem;
    background-color: #f9fafb;
}

/* 加载动画 */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 0.2em;
}

/* 页脚样式 */
footer {
    margin-top: 2.5rem !important;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.95rem;
}

footer h5 {
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--primary-color);
}

footer a {
    transition: all var(--transition-speed);
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header h5 {
        font-size: 1rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .navbar {
        padding: 0.5rem 1rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
}
