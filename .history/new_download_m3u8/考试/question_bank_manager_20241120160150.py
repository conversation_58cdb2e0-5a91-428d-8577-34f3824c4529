import json
import re
import logging
import hashlib
from pathlib import Path

class QuestionBankManager:
    def __init__(self, db_file='question_bank.json'):
        self.db_file = db_file
        self.question_bank = {
            'single_choice': {},  # 单选题
            'multiple_choice': {},  # 多选题
            'judge': {},  # 判断题
            'other': {}  # 其他类型
        }
        self.load_database()
    
    def load_database(self):
        """加载题库数据库"""
        try:
            if Path(self.db_file).exists():
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    self.question_bank = json.load(f)
                logging.info(f"成功加载题库，共有 {len(self.question_bank)} 个题目")
            else:
                logging.info("题库文件不存在，将创建新题库")
                self.question_bank = {}
        except Exception as e:
            logging.error(f"加载题库时出错: {e}")
            self.question_bank = {}
    
    def save_database(self):
        """保存题库到文件"""
        try:
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.question_bank, f, ensure_ascii=False, indent=4)
            logging.info(f"题库已保存，共包含 {len(self.question_bank)} 个题目")
            return True
        except Exception as e:
            logging.error(f"保存题库时出错: {e}")
            return False
    
    def identify_question_type(self, question_text):
        """识别题目类型"""
        # 判断题特征
        if re.search(r'这句话对吗|说法正确吗|说法对吗|正确吗|对吗|描述正确吗|判断题', question_text):
            return 'judge'
        # 多选题特征
        elif '多选题' in question_text or re.search(r'下列.*哪些', question_text):
            return 'multiple_choice'
        # 单选题特征
        elif '单选题' in question_text or re.search(r'下列.*哪个|下列.*哪项|以下.*哪个|以下.*哪项', question_text):
            return 'single_choice'
        # 其他类型
        return 'other'
    
    def process_question(self, question_text):
        """处理单个题目，提取必要信息"""
        try:
            # 提取题目内容
            title = self.extract_title(question_text)
            # 提取答案
            answer = self.extract_answer(question_text)
            # 识别题目类型
            q_type = self.identify_question_type(question_text)
            # 生成哈希值
            hash_value = self.create_hash(title)
            
            return {
                'question': question_text,
                'title': title,
                'answer': answer,
                'hash': hash_value,
                'type': q_type,
                'options': self.extract_options(question_text)
            }
        except Exception as e:
            logging.error(f"处理题目时出错: {e}")
            return None
    
    def extract_options(self, text):
        """提取选项"""
        options = []
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if re.match(r'^[A-D]\.', line):
                options.append(line)
        return options
    
    def add_question(self, question_data):
        """添加题目到题库"""
        try:
            if not question_data:
                return False
                
            hash_value = question_data['hash']
            q_type = question_data['type']
            
            self.question_bank[q_type][hash_value] = {
                'question': question_data['question'],
                'title': question_data['title'],
                'answer': question_data['answer'],
                'options': question_data['options']
            }
            return True
        except Exception as e:
            logging.error(f"添加题目时出错: {e}")
            return False
    
    def update_database(self, questions):
        """更新题库"""
        try:
            success_count = 0
            for question in questions:
                if not question.strip():
                    continue
                    
                question_data = self.process_question(question)
                if question_data and self.add_question(question_data):
                    success_count += 1
            
            if success_count > 0:
                self.save_database()
                
            return success_count
        except Exception as e:
            logging.error(f"更新题库时出错: {e}")
            return 0
    
    @staticmethod
    def extract_title(text):
        """提取题目标题"""
        text = re.sub(r'\s+', ' ', text.strip())
        # 匹配题目格式
        question_match = re.match(r'^\d+[\.、]\s*(.+?)(?=\s*(?:单选题|多选题|判断题|\(\d+分\)|A\.|$))', text)
        if question_match:
            return question_match.group(1).strip()
        return text
    
    @staticmethod
    def extract_answer(text):
        """提取答案"""
        # 查找答案标记
        answer_match = re.search(r'答案[:：\s]*([A-D])', text)
        if answer_match:
            return answer_match.group(1)
            
        # 查找选项标记
        lines = text.split('\n')
        for line in lines:
            if re.match(r'^[A-D]\.', line.strip()):
                return line.strip()
        return ""
    
    @staticmethod
    def create_hash(text):
        """创建题目哈希值"""
        # 清理文本
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', text.lower())
        return hashlib.md5(cleaned.encode('utf-8')).hexdigest()
    
    def get_question(self, hash_value):
        """获取题目信息"""
        return self.question_bank.get(hash_value)
    
    def get_all_questions(self):
        """获取所有题目"""
        return self.question_bank
    
    def get_statistics(self):
        """获取题库统计信息"""
        total = 0
        type_counts = {}
        
        for q_type, questions in self.question_bank.items():
            count = len(questions)
            type_counts[q_type] = count
            total += count
        
        return {
            'total_questions': total,
            'by_type': type_counts
        }
    
    def get_questions_by_type(self, q_type):
        """获取指定类型的所有题目"""
        return self.question_bank.get(q_type, {})
    
    def search_questions(self, keyword, q_type=None):
        """搜索题目"""
        results = []
        search_bank = self.question_bank[q_type] if q_type else {
            k: v for d in self.question_bank.values() for k, v in d.items()
        }
        
        for hash_value, question in search_bank.items():
            if keyword.lower() in question['title'].lower():
                results.append(question)
        return results
    
    def add_question_with_type(self, question_text, q_type):
        """添加带类型的题目到题库"""
        try:
            # 提取题目内容和答案
            title = self.extract_title(question_text)
            answer = self.extract_answer(question_text)
            options = self.extract_options(question_text)
            hash_value = self.create_hash(title)
            
            # 确保题型分类存在
            if q_type not in self.question_bank:
                self.question_bank[q_type] = {}
            
            # 存储题目信息
            self.question_bank[q_type][hash_value] = {
                'question': question_text,
                'title': title,
                'answer': answer,
                'options': options,
                'type': q_type
            }
            
            logging.info(f"添加题目到 {q_type} 类型: {title}")
            return True
            
        except Exception as e:
            logging.error(f"添加题目时出错: {e}")
            return False