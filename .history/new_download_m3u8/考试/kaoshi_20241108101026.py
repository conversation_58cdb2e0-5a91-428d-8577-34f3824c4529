import difflib
from docx import Document
from fuzzywuzzy import fuzz
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# 读取文本文件
def read_txt_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.readlines()

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题目和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错误: 不支持的文件格式，请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 匹配题目与参考答案
def match_question_to_answer(questions, reference_questions):
    matched_answers = []
    for i, question in enumerate(questions):
        if i < len(reference_questions):
            matched_answers.append((question, reference_questions[i], 100))  # 按顺序匹配参考答案
        else:
            matched_answers.append((question, "未找到匹配的参考答案", 0))
    return matched_answers

# 生成比较后的答案文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        # 添加标题
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建表格
        table = doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        # 表格居中对齐
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '题目'
        hdr_cells[1].text = '匹配的参考答案'
        hdr_cells[2].text = '匹配相似度'
        
        for idx, (question, answer, score) in enumerate(matched_answers, start=1):
            if question.strip() and answer.strip():  # 排除空白项
                row_cells = table.add_row().cells
                row_cells[0].text = question
                row_cells[1].text = f"{idx}. {answer.strip()}"
                row_cells[2].text = f"{score}%"
                
                # 设置单元格字体大小
                for cell in row_cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.size = Pt(10)
        
        # 添加一些视觉分隔符，使得表格更易读
        for row in table.rows:
            for cell in row.cells:
                cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
        doc.save(output_path)
    except PermissionError:
        print(f"错误: 无法保存文件到 '{output_path}'，请检查是否有写入权限或文件是否正在被其他程序使用。")
    except Exception as e:
        print(f"错误: 保存文件时发生意外错误: {e}")

# 主函数
def main():
    questions_file = input('请输入题目文件路径 (.txt 或 .docx): ').strip()
    print(f"你输入的题目文件路径是: {questions_file}")  # 调试信息
    reference_file = input('请输入题库文件路径 (.txt 或 .docx): ').strip()
    print(f"你输入的题库文件路径是: {reference_file}")  # 调试信息
    output_file = input('请输入输出文件路径 (.docx): ').strip()
    print(f"你输入的输出文件路径是: {output_file}")  # 调试信息

    # 验证文件路径是否有效
    if not os.path.isfile(questions_file):
        print(f"错误: 题目文件路径 '{questions_file}' 无效或不存在。")
        return
    if not os.path.isfile(reference_file):
        print(f"错误: 题库文件路径 '{reference_file}' 无效或不存在。")
        return
    if os.path.isdir(output_file) or ' ' in output_file:
        print(f"错误: 输出文件路径 '{output_file}' 无效，请确保路径正确且不包含空格。")
        return

    # 加载题目和题库
    try:
        questions = load_questions(questions_file)
        reference_questions = load_questions(reference_file)
    except ValueError as e:
        print(e)
        return

    # 比对题目和答案
    matched_answers = match_question_to_answer(questions, reference_questions)

    # 生成结果文件
    generate_result_file(matched_answers, output_file)
    print(f'比对结果已保存到 {output_file}')

if __name__ == "__main__":
    main()
