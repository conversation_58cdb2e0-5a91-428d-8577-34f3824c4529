import json
import re
import logging
import hashlib
from pathlib import Path

class QuestionBankManager:
    def __init__(self, db_file='question_bank.json'):
        self.db_file = db_file
        self.question_bank = {
            'single_choice': {},  # 单选题
            'multiple_choice': {},  # 多选题
            'judge': {},  # 判断题
            'other': {}  # 其他类型
        }
        self.load_database()
    
    def load_database(self):
        """加载题库数据库"""
        try:
            if Path(self.db_file).exists():
                print(f"正在加载题库文件: {self.db_file}")  # 调试输出
                
                # 读取文件内容
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"文件内容长度: {len(content)}")  # 调试输出
                    if not content.strip():
                        print("文件内容为空")
                        self.initialize_empty_database()
                        return
                    
                    data = json.loads(content)
                    print(f"加载的数据类型: {type(data)}")  # 调试输出
                
                # 初始化题库分类
                self.question_bank = {
                    'single_choice': {},
                    'multiple_choice': {},
                    'judge': {},
                    'other': {}
                }
                
                # 处理每个题目
                for q_type, questions in data.items():
                    print(f"处理 {q_type} 类型题目")  # 调试输出
                    if not isinstance(questions, dict):
                        print(f"警告: {q_type} 的数据不是字典类型")
                        continue
                    
                    for hash_value, question_data in questions.items():
                        if isinstance(question_data, dict):
                            # 获取题目内容
                            content = question_data.get('content', '')
                            title = question_data.get('title', '')
                            answer = question_data.get('answer', '')
                            options = question_data.get('options', [])
                            
                            if content or title:
                                self.question_bank[q_type][hash_value] = {
                                    'question': content,
                                    'title': title or content,
                                    'content': content,
                                    'answer': answer,
                                    'options': options,
                                    'type': q_type
                                }
                                print(f"添加题目: {title or content[:50]}...")  # 调试输出
                
                total_questions = sum(len(questions) for questions in self.question_bank.values())
                print(f"成功加载 {total_questions} 个题目")  # 调试输出
                logging.info(f"成功加载题库，共有 {total_questions} 个题目")
                
            else:
                print("题库文件不存在，创建新题库")  # 调试输出
                self.initialize_empty_database()
                
        except Exception as e:
            print(f"加载题库时出错: {e}")  # 调试输出
            logging.error(f"加载题库时出错: {e}")
            self.initialize_empty_database()
    
    def initialize_empty_database(self):
        """初始化空题库"""
        self.question_bank = {
            'single_choice': {},
            'multiple_choice': {},
            'judge': {},
            'other': {}
        }
        print("已初始化空题库")  # 调试输出
    
    def save_database(self):
        """保存题库到文件"""
        try:
            print("正在保存题库...")  # 调试输出
            
            # 在保存之前打印调试信息
            total_questions = sum(len(questions) for questions in self.question_bank.values())
            print(f"准备保存 {total_questions} 个题目")
            
            for q_type, questions in self.question_bank.items():
                print(f"\n{q_type} 类型题目数量: {len(questions)}")
                for hash_value, question in questions.items():
                    print(f"题目: {question['title'][:50]}...")
                    print(f"选项: {question.get('options', [])}")
                    print(f"答案: {question.get('answer', '')}")
                    print("-" * 50)

            # 保存到文件
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.question_bank, f, ensure_ascii=False, indent=4)
            
            print(f"题库保存成功，共 {total_questions} 个题目")  # 调试输出
            logging.info(f"题库已保存，共包含 {total_questions} 个题目")
            return True
            
        except Exception as e:
            print(f"保存题库时出错: {e}")  # 调试输出
            logging.error(f"保存题库时出错: {e}")
            return False
    
    def identify_question_type(self, question_text):
        """识别题目类型"""
        # 判断题特征
        if re.search(r'这句话对吗|说确吗|说法对吗|正确吗|对吗|描述正确吗|判断题', question_text):
            return 'judge'
        # 多选题特征
        elif '多选题' in question_text or re.search(r'下列.*哪些', question_text):
            return 'multiple_choice'
        # 单选题特征
        elif '单选题' in question_text or re.search(r'下列.*哪个|下列.*哪项|以下.*哪个|以下.*哪项', question_text):
            return 'single_choice'
        # 其他类型
        return 'other'
    
    def process_question(self, question_text):
        """处理单个题目，提取必要信息"""
        try:
            # 提取题目内容
            title = self.extract_title(question_text)
            # 提取答案
            answer = self.extract_answer(question_text)
            # 识别题目类型
            q_type = self.identify_question_type(question_text)
            # 生成哈希值
            hash_value = self.create_hash(title)
            
            return {
                'question': question_text,
                'title': title,
                'answer': answer,
                'hash': hash_value,
                'type': q_type,
                'options': self.extract_options(question_text)
            }
        except Exception as e:
            logging.error(f"处理题目出错: {e}")
            return None
    
    def extract_options(self, text):
        """提取选项"""
        options = []
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if re.match(r'^[A-D]\.', line):
                option = re.sub(r'^[A-D]\.', '', line).strip()
                options.append(line)  # 保留完整的选项文本，包括选项标记
        return options
    
    def add_question(self, question_data):
        """添加题目到题库"""
        try:
            if not question_data:
                return False
                
            hash_value = question_data['hash']
            q_type = question_data['type']
            
            self.question_bank[q_type][hash_value] = {
                'question': question_data['question'],
                'title': question_data['title'],
                'answer': question_data['answer'],
                'options': question_data['options']
            }
            return True
        except Exception as e:
            logging.error(f"添加题目时出错: {e}")
            return False
    
    def update_database(self, questions):
        """更新题库"""
        try:
            success_count = 0
            for question in questions:
                if not question.strip():
                    continue
                    
                question_data = self.process_question(question)
                if question_data and self.add_question(question_data):
                    success_count += 1
            
            if success_count > 0:
                self.save_database()
                
            return success_count
        except Exception as e:
            logging.error(f"更新题库时出错: {e}")
            return 0
    
    @staticmethod
    def extract_title(text):
        """提取题目标题"""
        text = re.sub(r'\s+', ' ', text.strip())
        # 匹配题目格式
        question_match = re.match(r'^\d+[\.、]\s*(.+?)(?=\s*(?:单选题|多选题|判断题|\(\d+分\)|A\.|$))', text)
        if question_match:
            return question_match.group(1).strip()
        return text
    
    def extract_answer(self, text):
        """提取答案"""
        # 查找答案标记
        answer_match = re.search(r'答案[:：\s]*([A-D])', text)
        if answer_match:
            return answer_match.group(1)
            
        # 查找选项标记
        lines = text.split('\n')
        for line in lines:
            if re.match(r'^[A-D]\.', line.strip()):
                return line.strip()
        return ""
    
    @staticmethod
    def create_hash(text):
        """创建题目哈希值"""
        # 清理文本
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', text.lower())
        return hashlib.md5(cleaned.encode('utf-8')).hexdigest()
    
    def get_question(self, hash_value):
        """获取题目信息"""
        return self.question_bank.get(hash_value)
    
    def get_all_questions(self):
        """获取所有题目"""
        try:
            # 强制重新加载题库
            self.load_database()
            
            # 调试输出
            total_questions = sum(len(questions) for questions in self.question_bank.values())
            print(f"题库中总题目数: {total_questions}")
            for q_type, questions in self.question_bank.items():
                print(f"{q_type} 类型题目数: {len(questions)}")
            
            return self.question_bank
        except Exception as e:
            logging.error(f"获取题库数据时出错: {e}")
            print(f"获取题库数据时出错: {e}")  # 调试输出
            return {
                'single_choice': {},
                'multiple_choice': {},
                'judge': {},
                'other': {}
            }
    
    def get_statistics(self):
        """获取题库统计信息"""
        stats = {
            'total_questions': 0,
            'by_type': {
                'single_choice': 0,
                'multiple_choice': 0,
                'judge': 0,
                'other': 0
            }
        }
        
        # 统计每种类型的题目数量
        for q_type, questions in self.question_bank.items():
            count = len(questions)
            stats['by_type'][q_type] = count
            stats['total_questions'] += count
        
        return stats
    
    def get_questions_by_type(self, q_type):
        """获取指定类型的所有题目"""
        questions = self.question_bank.get(q_type, {})
        # 转换为列表并添加序号
        questions_list = []
        for i, (hash_value, question) in enumerate(questions.items(), 1):
            question['index'] = i  # 添加序号
            questions_list.append(question)
        return questions_list
    
    def search_questions(self, keyword, q_type=None):
        """搜索题目"""
        results = []
        search_bank = self.question_bank[q_type] if q_type else {
            k: v for d in self.question_bank.values() for k, v in d.items()
        }
        
        for hash_value, question in search_bank.items():
            if keyword.lower() in question['title'].lower():
                results.append(question)
        return results
    
    def add_question_with_type(self, question_text, q_type):
        """添加带类型的题目到题库"""
        try:
            print(f"正在添加题目: {question_text[:50]}...")  # 调试输出
            
            # 提取题目内容和答案
            title = self.extract_title(question_text)
            answer = self.extract_answer(question_text)
            options = self.extract_options(question_text)
            hash_value = self.create_hash(title)
            
            # 确保题型分类存在
            if q_type not in self.question_bank:
                self.question_bank[q_type] = {}
            
            # 存储题目信息
            self.question_bank[q_type][hash_value] = {
                'question': question_text,
                'title': title,
                'content': question_text,
                'answer': answer,
                'options': options,
                'type': q_type,
                'hash': hash_value
            }
            
            print(f"成功添加题目到 {q_type} 类型")  # 调试输出
            return True
            
        except Exception as e:
            print(f"添加题目时出错: {e}")  # 调试输出
            logging.error(f"添加题目时出错: {e}")
            return False
    
    def clear_database(self):
        """清空题库"""
        try:
            # 重置题库数据结构
            self.question_bank = {
                'single_choice': {},
                'multiple_choice': {},
                'judge': {},
                'other': {}
            }
            
            # 确保完全清空文件
            empty_data = {
                'single_choice': {},
                'multiple_choice': {},
                'judge': {},
                'other': {}
            }
            
            # 先清空文件内容
            with open(self.db_file, 'w', encoding='utf-8') as f:
                f.write('')  # 先完全清空文件
            
            # 然后写入空的数据结构
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(empty_data, f, ensure_ascii=False, indent=4)
            
            # 强制重新加载数据库
            self.load_database()
            
            # 验证数据是否真的清空了
            total_questions = sum(len(questions) for questions in self.question_bank.values())
            if total_questions > 0:
                raise Exception("数据未能完全清空")
            
            logging.info("题库已完全清空")
            return True
        except Exception as e:
            logging.error(f"清空题库时出错: {e}")
            return False
    
    def generate_question_hash(self, question_obj):
        """生成题目的唯一hash值"""
        try:
            # 组合题目的关键信息
            question_text = question_obj.get('question', '').strip().lower()
            answer = question_obj.get('answer', '').strip().lower()
            options = [opt.strip().lower() for opt in question_obj.get('options', [])]
            options_text = ''.join(sorted(options))  # 排序选项以确保一致性
            
            # 组合所有信息生成hash
            hash_content = f"{question_text}{answer}{options_text}"
            return hashlib.md5(hash_content.encode('utf-8')).hexdigest()
        except Exception as e:
            logging.error(f"生成题目hash时出错: {e}")
            return None
    
    def add_question(self, question_obj):
        """添加题目到题库"""
        try:
            # 生成hash值
            question_hash = self.generate_question_hash(question_obj)
            if not question_hash:
                return False

            # 检查是否已存在相同的题目
            for q_type in self.question_bank.values():
                for existing_q in q_type:
                    existing_hash = self.generate_question_hash(existing_q)
                    if existing_hash == question_hash:
                        logging.info(f"跳过重复题目: {question_obj.get('question', '')[:50]}...")
                        return False

            # 添加hash值到题目对象
            question_obj['hash'] = question_hash
            
            # 确定题目类型并添加
            q_type = self.determine_question_type(question_obj)
            if q_type in self.question_bank:
                self.question_bank[q_type].append(question_obj)
                return True
            return False

        except Exception as e:
            logging.error(f"添加题目时出错: {e}")
            return False
    
    def determine_question_type(self, question_obj):
        """确定题目类型"""
        question_text = question_obj.get('question', '').lower()
        answer = question_obj.get('answer', '').upper()
        
        if '这句话是否正确' in question_text or '这句话对吗' in question_text:
            return 'judge'
        elif answer and len(answer) > 1 and all(c in 'ABCD' for c in answer):
            return 'multiple_choice'
        elif answer and answer in 'ABCD':
            return 'single_choice'
        return 'other'
    
    def load_database(self):
        """从文件加载题库"""
        try:
            file_path = os.path.join(os.path.dirname(__file__), 'question_bank.json')
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 验证数据结构
                    if all(key in data for key in self.question_bank.keys()):
                        self.question_bank = data
                        return True
            return False
        except Exception as e:
            logging.error(f"加载题库时出错: {e}")
            return False