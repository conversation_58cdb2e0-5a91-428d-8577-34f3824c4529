import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import json
import hashlib
import logging

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 读取文本文件
def read_txt_file(file_path):
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return [line.strip() for line in file.readlines() if line.strip()]
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题目和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错: 不支持的文件格式，请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 生成题目的哈希值
def generate_question_hash(question_text):
    """为题目生成唯一的哈希值"""
    # 移除空白字符和标点符号，只保留题目的主要内容
    cleaned_text = ''.join(c for c in question_text if c not in '.,?!；，。？！\n\r\t ')
    # 生成hash
    return hashlib.md5(cleaned_text.encode('utf-8')).hexdigest()

# 处理题库文件，按题型分类并保存为JSON
def process_reference_questions(reference_file):
    """处理题库文件，按题型分类并保存为JSON"""
    logging.info(f"开始处理题库文件: {reference_file}")
    
    try:
        lines = load_questions(reference_file)
        logging.info(f"成功加载题库文件，共 {len(lines)} 行")
    except Exception as e:
        logging.error(f"加载文件失败: {str(e)}")
        return None
    
    question_bank = {
        "单选题": [],
        "多选题": [],
        "判断题": []
    }
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 跳过空行
        if not line:
            i += 1
            continue
            
        # 如果是题目（以问号或句号结尾）
        if ('？' in line or '。' in line) and not line.startswith(('A.', 'B.', 'C.', 'D.')):
            question = line
            options = []
            answer = None
            
            # 向后查找选项和答案
            j = i + 1
            while j < len(lines):
                next_line = lines[j].strip()
                if not next_line:
                    j += 1
                    continue
                    
                # 如果遇到下一个题目，停止查找
                if ('？' in next_line or '。' in next_line) and not next_line.startswith(('A.', 'B.', 'C.', 'D.')):
                    break
                    
                # 收集选项
                if next_line.startswith(('A.', 'B.', 'C.', 'D.')):
                    options.append(next_line)
                # 收集答案（通常是单个字母或"正确"/"错误"）
                elif (next_line.startswith(('A', 'B', 'C', 'D')) and len(next_line.strip()) <= 5) or \
                     next_line in ['正确', '错误']:
                    answer = next_line
                
                j += 1
            
            # 确定题目类型并保存
            if answer:
                question_type = None
                if answer in ['正确', '错误']:
                    question_type = "判断题"
                    if not options:
                        options = ['正确', '错误']
                elif len(answer) > 1 and all(c in 'ABCD' for c in answer):
                    question_type = "多选题"
                else:
                    question_type = "单选题"
                
                question_hash = generate_question_hash(question)
                question_data = {
                    "hash": question_hash,
                    "question": question,
                    "answer": answer,
                    "options": options
                }
                
                question_bank[question_type].append(question_data)
                logging.info(f"添加{question_type}: {question}")
                logging.debug(f"答案: {answer}")
                logging.debug(f"选项: {options}")
        
        i += 1
    
    # 输出统计信息
    logging.info("\n题库统计：")
    total_questions = 0
    for qtype, questions in question_bank.items():
        count = len(questions)
        logging.info(f"{qtype}：{count}题")
        total_questions += count
    logging.info(f"总计：{total_questions}题")
    
    # 保存到JSON文件
    try:
        with open('question_bank.json', 'w', encoding='utf-8') as f:
            json.dump(question_bank, f, ensure_ascii=False, indent=4)
        logging.info("题库已保存到 question_bank.json")
    except Exception as e:
        logging.error(f"保存题库时出错: {str(e)}")
    
    return question_bank

def save_question(question_bank, current_type, current_question, current_answer, current_options):
    """保存题目到题库"""
    if not current_type:
        logging.warning(f"题目没有类型标记: {current_question}")
        return
        
    logging.debug(f"保存题目:")
    logging.debug(f"类型: {current_type}")
    logging.debug(f"题目: {current_question}")
    logging.debug(f"选项: {current_options}")
    logging.debug(f"答案: {current_answer}")
    
    question_hash = generate_question_hash(current_question)
    question_data = {
        "hash": question_hash,
        "question": current_question,
        "answer": current_answer,
        "options": current_options
    }
    
    question_bank[current_type].append(question_data)
    logging.info(f"已添加{current_type}")

# 从JSON文件加载题库
def load_question_bank():
    try:
        with open('question_bank.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("题库文件不存在，请先创建题库")
        return None
    except json.JSONDecodeError:
        print("题库文件格式错误")
        return None

# 修改匹配函数
def match_question_to_answer(questions, question_bank):
    matched_answers = []
    current_type = None
    
    for question in questions:
        question = question.strip()
        if not question:
            continue
            
        # 检查是否是题型标记
        if '单选' in question:
            current_type = "单选题"
            matched_answers.append((question, "", 100))
            continue
        elif '多选' in question:
            current_type = "多选题"
            matched_answers.append((question, "", 100))
            continue
        elif '判断' in question:
            current_type = "判断题"
            matched_answers.append((question, "", 100))
            continue
            
        # 如果没有当前题型，继续下一个问题
        if not current_type:
            matched_answers.append((question, "未找到匹配的题型", 0))
            continue
            
        # 在对应题型中查找答案
        found = False
        for q_data in question_bank.get(current_type, []):
            # 清理题目文本进行比较
            bank_question = q_data['question'].strip()
            current_question = question.strip()
            
            # 移除题号进行比较
            if bank_question.find('.') != -1:
                bank_question = bank_question.split('.', 1)[1].strip()
            if current_question.find('.') != -1:
                current_question = current_question.split('.', 1)[1].strip()
            
            # 如果题目匹配
            if bank_question in current_question or current_question in bank_question:
                # 格式化答案输出
                answer_text = f"答案：{q_data['answer']}\n"
                if q_data['options']:
                    answer_text += "选项：\n" + "\n".join(q_data['options'])
                
                matched_answers.append((question, answer_text, 100))
                found = True
                break
                
        if not found:
            matched_answers.append((question, "未找到匹配的答案", 0))
    
    return matched_answers

# 生成比较后的案文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建四列表格
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '考试题目'
        hdr_cells[1].text = '题目类型'
        hdr_cells[2].text = '匹配答案'
        hdr_cells[3].text = '匹配状态'
        
        # 记录当前题目类型
        current_type = ""
        
        # 添加数据
        for question, answer, score in matched_answers:
            if not question.strip():
                continue
                
            # 检查是否是章节标题
            if any(keyword in question for keyword in ['单选', '判断', '多选']):
                current_type = question.strip()
                row_cells = table.add_row().cells
                row_cells[0].text = question.strip()
                row_cells[1].text = ""
                row_cells[2].text = ""
                row_cells[3].text = ""
                # 设置章节标题格式
                for cell in row_cells:
                    for paragraph in cell.paragraphs:
                        paragraph.paragraph_format.space_before = Pt(12)
                        paragraph.paragraph_format.space_after = Pt(12)
                        for run in paragraph.runs:
                            run.font.bold = True
            else:
                row_cells = table.add_row().cells
                row_cells[0].text = question.strip()
                # 设置题目类型
                if '单选' in current_type:
                    row_cells[1].text = "单选题"
                elif '判断' in current_type:
                    row_cells[1].text = "判断题"
                elif '多选' in current_type:
                    row_cells[1].text = "多选题"
                else:
                    row_cells[1].text = ""
                row_cells[2].text = answer.strip()
                # 设置匹配状态
                if score == 100:
                    row_cells[3].text = "匹配成功"
                else:
                    row_cells[3].text = "未匹配"
        
        # 设置表格格式
        for row in table.rows:
            # 设置列宽
            row.cells[0].width = Pt(200)  # 考试题目列宽
            row.cells[1].width = Pt(80)   # 题目类型列宽
            row.cells[2].width = Pt(200)  # 匹配答案列宽
            row.cells[3].width = Pt(70)   # 匹配状态列宽
            
            # 设置单元格格式
            for cell in row.cells:
                paragraphs = cell.paragraphs
                for paragraph in paragraphs:
                    paragraph.paragraph_format.space_before = Pt(6)
                    paragraph.paragraph_format.space_after = Pt(6)
                    for run in paragraph.runs:
                        run.font.size = Pt(10)
        
        # 设置表头格式
        for cell in table.rows[0].cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.bold = True
                    run.font.size = Pt(11)
        
        doc.save(output_path)
    except Exception as e:
        print(f"保存文件时发生错误: {e}")

# 主函数
def main():
    try:
        print("\n=== 题目与答案匹配工具 ===\n")
        
        # 检查是否需要创建或更��题库
        create_bank = input('是否需要创建/更新题库？(y/n): ').strip().lower()
        if create_bank == 'y':
            while True:
                reference_file = input('请输入题库文件路径 (.txt 或 .docx): ').strip()
                if os.path.isfile(reference_file):
                    question_bank = process_reference_questions(reference_file)
                    print("题库已更新")
                    break
                print(f"错误: 文件不存在，请重新输入")
        
        # 加载题库
        question_bank = load_question_bank()
        if not question_bank:
            return
            
        # 输入考试题目文件
        while True:
            questions_file = input('请输入题目文件路径 (.txt 或 .docx): ').strip()
            if os.path.isfile(questions_file):
                break
            print(f"错误: 文件不存在，请重新输入")

        # 输入输出文件路径
        while True:
            output_file = input('请输入输出文件路径 (.docx): ').strip()
            if not output_file.startswith('/'):
                output_file = '/' + output_file
            if not output_file.endswith('.docx'):
                output_file += '.docx'
                
            output_dir = os.path.dirname(output_file)
            if not os.path.exists(output_dir):
                print(f"错误: 输出目录 '{output_dir}' 不存在，请重新输入")
                continue
            break

        # 加载考试题目
        print("\n正在加载文件...")
        questions = load_questions(questions_file)
        print(f"已加载题目 {len(questions)} 个")

        # 比对答案
        print("正在比对题目和答案...")
        matched_answers = match_question_to_answer(questions, question_bank)

        # 生成结果
        print("正在生成结果文件...")
        generate_result_file(matched_answers, output_file)
        print(f'\n完成！结果已保存到: {output_file}')
        
    except KeyboardInterrupt:
        print("\n程序已取消")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        print("请确保输入的文件路径正确，并且有写入权限。")

if __name__ == "__main__":
    main()
