import pandas as pd
from typing import Generator, Dict, Any
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
import re

class QuestionDataManager:
    def __init__(self, parent, bank_manager, msg_manager):
        self.parent = parent
        self.bank_manager = bank_manager
        self.msg_manager = msg_manager
        self.batch_size = 100  # 每批处理的数据量
        self.page_size = 20    # 每页显示的题目数量
        self.current_page = 1  # 当前页码
        self.questions_data = []  # 存储所有题目数据
    
    def question_generator(self, file_path: str) -> Generator[Dict[str, Any], None, None]:
        """使用生成器读取题目文件"""
        try:
            # 根据文件类型选择不同的读取方式
            if file_path.endswith('.txt'):
                yield from self._read_txt_generator(file_path)
            elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                yield from self._read_excel_generator(file_path)
            elif file_path.endswith('.csv'):
                yield from self._read_csv_generator(file_path)
        except Exception as e:
            self.msg_manager.add_message(f"读取文件时出错: {str(e)}", "error")
    
    def _read_txt_generator(self, file_path: str) -> Generator[Dict[str, Any], None, None]:
        """读取文本文件的生成器"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 使用空行分割题目
            raw_questions = content.split('\n\n')
            question_count = 0
            
            for raw_question in raw_questions:
                # 跳过空内容
                if not raw_question.strip():
                    continue
                    
                # 处理题目内容
                lines = raw_question.strip().split('\n')
                question_lines = []
                answer_lines = []
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                        
                    # 如果是答案行
                    if line.startswith('A.'):
                        answer_lines.append(line)
                    else:
                        question_lines.append(line)
                
                # 如果有有效内容
                if question_lines:
                    question_count += 1
                    question_content = '\n'.join(question_lines)
                    answer_content = '\n'.join(answer_lines) if answer_lines else ''
                    
                    # 确定题目类型
                    if '这句话对吗' in question_content or '这句话正确吗' in question_content:
                        q_type = 'judge'
                    elif '多选题' in question_content or re.search(r'下列.*哪些', question_content):
                        q_type = 'multiple_choice'
                    elif '单选题' in question_content or re.search(r'下列.*哪个|下列.*哪项', question_content):
                        q_type = 'single_choice'
                    else:
                        q_type = 'other'
                    
                    yield {
                        'index': question_count,
                        'content': question_content,
                        'answer': answer_content,
                        'type': q_type
                    }
                    
                    # 调试输出
                    print(f"\n处理题目 {question_count}:")
                    print("题目内容:")
                    print(question_content)
                    print("答案内容:")
                    print(answer_content)
                    print("-" * 50)
                    
        except Exception as e:
            self.msg_manager.add_message(f"读取文本文件时出错: {str(e)}", "error")
    
    def _determine_question_type(self, question_text: str) -> str:
        """确定题目类型"""
        if '这句话对吗' in question_text or '这句话正确吗' in question_text:
            return 'judge'
        elif '多选题' in question_text or re.search(r'下列.*哪些', question_text):
            return 'multiple_choice'
        elif '单选题' in question_text or re.search(r'下列.*哪个|下列.*哪项', question_text):
            return 'single_choice'
        return 'other'
    
    def _read_excel_generator(self, file_path: str) -> Generator[Dict[str, Any], None, None]:
        """读取Excel文件的生成器"""
        try:
            # 使用pandas读取Excel文件，每次读取一部分
            for chunk in pd.read_excel(file_path, chunksize=self.batch_size):
                for index, row in chunk.iterrows():
                    yield {
                        'index': index + 1,
                        'content': str(row['content']) if 'content' in row else str(row[0]),
                        'type': str(row['type']) if 'type' in row else 'other'
                    }
        except Exception as e:
            self.msg_manager.add_message(f"读取Excel文件时出错: {str(e)}", "error")
    
    def _read_csv_generator(self, file_path: str) -> Generator[Dict[str, Any], None, None]:
        """读取CSV文件的生成器"""
        try:
            for chunk in pd.read_csv(file_path, chunksize=self.batch_size):
                for index, row in chunk.iterrows():
                    yield {
                        'index': index + 1,
                        'content': str(row['content']) if 'content' in row else str(row[0]),
                        'type': str(row['type']) if 'type' in row else 'other'
                    }
        except Exception as e:
            self.msg_manager.add_message(f"读取CSV文件时出错: {str(e)}", "error")
    
    def import_questions(self):
        """导入题目"""
        try:
            file_path = filedialog.askopenfilename(
                title='选择题目文件',
                filetypes=[
                    ("All supported files", "*.txt;*.xlsx;*.xls;*.csv"),
                    ("Text files", "*.txt"),
                    ("Excel files", "*.xlsx;*.xls"),
                    ("CSV files", "*.csv")
                ]
            )
            
            if not file_path:
                return
            
            # 检查是否存在现有数据
            existing_questions = self.bank_manager.get_statistics()['total_questions']
            if existing_questions > 0:
                # 询问用户操作方式
                message = (
                    f"当前题库中已有 {existing_questions} 个题目\n"
                    "是否要追加新题目？\n\n"
                    "选择[是] - 追加新题目\n"
                    "选择[否] - 覆盖现有题库\n"
                    "选择[取消] - 取消操作"
                )
                choice = messagebox.askyesnocancel("题库操作选择", message)
                
                if choice is None:  # 取消操作
                    return
                elif choice is False:  # 覆盖现有题库
                    self.bank_manager.clear_database()
                    self.msg_manager.add_message("已清空现有题库", "info")
            
            # 创建进度窗口
            progress_window = tk.Toplevel(self.parent)
            progress_window.title("导入进度")
            progress_window.geometry("400x150")
            
            # 进度标签
            progress_label = ttk.Label(progress_window, text="正在导题目...")
            progress_label.pack(pady=10)
            
            # 进度条
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                progress_window, 
                variable=progress_var,
                maximum=100
            )
            progress_bar.pack(fill=tk.X, padx=20, pady=10)
            
            # 计数标签
            count_label = ttk.Label(progress_window, text="已导入: 0 题")
            count_label.pack(pady=10)
            
            # 使用生成器导入数据
            imported_count = 0
            questions_data = []
            
            for question in self.question_generator(file_path):
                imported_count += 1
                questions_data.append(question)
                
                # 更新进度
                if imported_count % 10 == 0:  # 每10题更新一次显示
                    progress_var.set((imported_count % 100))
                    count_label.config(text=f"已导入: {imported_count} 题")
                    progress_window.update()
                
                # 每批次处理数据
                if len(questions_data) >= self.batch_size:
                    self._process_batch(questions_data)
                    questions_data = []
            
            # 处理剩余的数据
            if questions_data:
                self._process_batch(questions_data)
            
            # 关闭进度窗口
            progress_window.destroy()
            
            # 刷新显示
            self.refresh_question_list()
            self.msg_manager.add_message(f"成功导入 {imported_count} 个题目", "success")
            
            # 显示分类窗口
            self.show_data_manager()
            
        except Exception as e:
            self.msg_manager.add_message(f"导入题目时出错: {str(e)}", "error")
    
    def _process_batch(self, questions_data: list):
        """处理一批题目数据"""
        try:
            # 将新数据添加到总数据列表中
            self.questions_data.extend(questions_data)
            
            # 更新显示
            self.current_page = 1  # 重置到第一页
            self.update_page_display()
            
        except Exception as e:
            self.msg_manager.add_message(f"处理数据批次时出错: {str(e)}", "error")
    
    def show_data_manager(self):
        """显示数据管理窗口"""
        # 创建数据管理窗口
        self.window = tk.Toplevel(self.parent)
        self.window.title("题库数据管理")
        self.window.geometry("1400x800")
        
        # 配置警告按钮样式
        style = ttk.Style()
        style.configure('Warning.TButton', 
                       background='red',
                       foreground='red',
                       font=('Arial', 10, 'bold'))
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分隔面板
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：题目列表
        self.create_question_list_frame(paned)
        
        # 右侧：题目详情和操作
        self.create_detail_frame(paned)
        
        # 加载并显示数据
        self.load_and_display_data()
    
    def load_and_display_data(self):
        """加载并显示数据"""
        try:
            # 清空现有数据
            self.questions_data = []
            
            # 从题库管理器获取所有题目
            all_questions = self.bank_manager.get_all_questions()
            
            # 处理每种类型的题目
            for q_type, questions in all_questions.items():
                for hash_value, question in questions.items():
                    self.questions_data.append({
                        'content': question.get('content', ''),
                        'title': question.get('title', ''),
                        'type': q_type,
                        'options': question.get('options', []),
                        'answer': question.get('answer', '')
                    })
            
            # 更新显示
            self.current_page = 1
            self.update_page_display()
            
            # 显示统计信息
            total_questions = len(self.questions_data)
            self.msg_manager.add_message(f"已加载 {total_questions} 个题目", "info")
            
        except Exception as e:
            self.msg_manager.add_message(f"加载数据时出错: {str(e)}", "error")
    
    def create_question_list_frame(self, parent):
        """创建题目列表框架"""
        left_frame = ttk.LabelFrame(parent, text="题目列表")
        parent.add(left_frame, weight=2)
        
        # 创建工具栏
        toolbar = ttk.Frame(left_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加工具栏按钮
        ttk.Button(toolbar, text="导入新题目", 
                  command=self.import_questions).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="导出题库", 
                  command=self.export_questions).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="清空题库", 
                  command=self.clear_questions).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="重置数据", 
                  command=self.reset_data,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=5)
        
        # 创建分页控制框架
        page_control = ttk.Frame(left_frame)
        page_control.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加分页控制按钮
        self.prev_button = ttk.Button(page_control, text="上一页", 
                                    command=self.prev_page)
        self.prev_button.pack(side=tk.LEFT, padx=5)
        
        self.page_label = ttk.Label(page_control, text="第 1 页")
        self.page_label.pack(side=tk.LEFT, padx=5)
        
        self.next_button = ttk.Button(page_control, text="下一页", 
                                    command=self.next_page)
        self.next_button.pack(side=tk.LEFT, padx=5)
        
        # 创建表格框架
        table_frame = ttk.Frame(left_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建题目列表
        columns = ('序号', '题目内容', '类型')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # 设置列
        self.tree.heading('序号', text='序号')
        self.tree.heading('题目内容', text='题目内容')
        self.tree.heading('类型', text='类型')
        
        self.tree.column('序号', width=60, minwidth=60)
        self.tree.column('题目内容', width=800, minwidth=400)
        self.tree.column('类型', width=100, minwidth=100)
        
        # 添加滚动条
        y_scroll = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        x_scroll = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=y_scroll.set, xscrollcommand=x_scroll.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        y_scroll.grid(row=0, column=1, sticky='ns')
        x_scroll.grid(row=1, column=0, sticky='ew')
        
        # 配置网格权重
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_question_select)
        
    def create_detail_frame(self, parent):
        """创建详情框架"""
        right_frame = ttk.LabelFrame(parent, text="题目详情")
        parent.add(right_frame, weight=1)
        
        # 题目详情
        detail_label = ttk.Label(right_frame, text="题目内容", font=('Arial', 11, 'bold'))
        detail_label.pack(pady=(10,5), padx=5, anchor='w')
        
        self.detail_text = tk.Text(right_frame, wrap=tk.WORD, height=10)
        self.detail_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 题目类型选择
        type_frame = ttk.LabelFrame(right_frame, text="题目类型")
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.question_type = tk.StringVar(value="other")
        
        ttk.Radiobutton(type_frame, text="单选题", variable=self.question_type,
                       value="single_choice").pack(anchor='w')
        ttk.Radiobutton(type_frame, text="多选题", variable=self.question_type,
                       value="multiple_choice").pack(anchor='w')
        ttk.Radiobutton(type_frame, text="判断题", variable=self.question_type,
                       value="judge").pack(anchor='w')
        ttk.Radiobutton(type_frame, text="其他题型", variable=self.question_type,
                       value="other").pack(anchor='w')
        
        # 操作按钮
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(button_frame, text="保存修改", 
                  command=self.save_question).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除题目", 
                  command=self.delete_question).pack(side=tk.LEFT, padx=5)
    
    def export_questions(self):
        """导出题库"""
        # 实现导出逻辑
        pass
    
    def clear_questions(self):
        """清空题库"""
        if messagebox.askyesno("确认", "确定要清空题库吗？此操作不可恢复！"):
            self.bank_manager.clear_database()
            self.refresh_question_list()
            self.msg_manager.add_message("题库已清空", "info")
    
    def on_question_select(self, event):
        """选中题目时的处理"""
        try:
            selected = self.tree.selection()
            if not selected:
                return
            
            item = selected[0]
            item_index = self.tree.index(item)
            page_offset = (self.current_page - 1) * self.page_size
            actual_index = page_offset + item_index
            
            if actual_index < len(self.questions_data):
                question_data = self.questions_data[actual_index]
                
                # 显示��整题目内容
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.insert(tk.END, f"题目内容：\n{question_data['content']}\n\n")
                
                # 显示答案
                if question_data['answer']:
                    self.detail_text.insert(tk.END, f"答案：\n{question_data['answer']}")
                
                # 设置题目类型
                self.question_type.set(question_data['type'])
            
        except Exception as e:
            self.msg_manager.add_message(f"显示题目详情时出错: {str(e)}", "error")
    
    def save_question(self):
        """保存题目修改"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个题目")
            return
            
        # 实现保存逻辑
        pass
    
    def delete_question(self):
        """删除题目"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个题目")
            return
            
        if messagebox.askyesno("确认", "确定要删除选中的题目吗？"):
            # 实现删除逻辑
            pass
    
    def update_page_display(self):
        """更新分页显示"""
        try:
            # 清空当前显示
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            if not self.questions_data:
                self.msg_manager.add_message("没有数据可显示", "warning")
                return
            
            # 计算当前页的数据范围
            start_idx = (self.current_page - 1) * self.page_size
            end_idx = start_idx + self.page_size
            
            # 获取当前页的数据
            page_data = self.questions_data[start_idx:end_idx]
            
            # 填充数据
            for i, question in enumerate(page_data, start=start_idx + 1):
                self.tree.insert('', tk.END, values=(
                    i,
                    question['content'],  # 显示完整题目内容
                    question['type']
                ))
            
            # 更新页码显示
            total_pages = (len(self.questions_data) + self.page_size - 1) // self.page_size
            self.page_label.config(text=f"第 {self.current_page} / {total_pages} 页")
            
            # 更新按钮状态
            self.prev_button.config(state='normal' if self.current_page > 1 else 'disabled')
            self.next_button.config(state='normal' if self.current_page < total_pages else 'disabled')
            
            # 显示当前页的题目数量
            start_count = start_idx + 1
            end_count = min(end_idx, len(self.questions_data))
            total_count = len(self.questions_data)
            self.msg_manager.add_message(
                f"显示第 {start_count}-{end_count} 题，共 {total_count} 题",
                "info"
            )
            
        except Exception as e:
            self.msg_manager.add_message(f"更新显示时出错: {str(e)}", "error")
    
    def next_page(self):
        """下一页"""
        total_pages = (len(self.questions_data) + self.page_size - 1) // self.page_size
        if self.current_page < total_pages:
            self.current_page += 1
            self.update_page_display()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_page_display()
    
    def refresh_question_list(self):
        """刷新题目列表"""
        # 清空数据
        self.questions_data = []
        
        # 重新加载题目
        questions = self.bank_manager.get_all_questions()
        for q_type, type_questions in questions.items():
            for hash_value, question in type_questions.items():
                self.questions_data.append({
                    'content': question['title'],
                    'type': question['type']
                })
        
        # 更新显示
        self.current_page = 1
        self.update_page_display()
    
    def reset_data(self):
        """重置数据功能"""
        if messagebox.askyesno("确认重置", 
                             "确定要重置所有数据吗？\n这将清空当前所有题目分类，恢复到初始状态。\n此操作不可恢复！",
                             icon='warning'):
            try:
                # 清空题库
                self.bank_manager.clear_database()
                
                # 清空当前数据
                self.questions_data = []
                
                # 更新显示
                self.current_page = 1
                self.update_page_display()
                
                # 清空详情显示
                if hasattr(self, 'detail_text'):
                    self.detail_text.delete(1.0, tk.END)
                
                # 重置类型选择
                if hasattr(self, 'question_type'):
                    self.question_type.set('other')
                
                # 显示成功消息
                self.msg_manager.add_message("数据已完全重置", "success")
                messagebox.showinfo("成功", "数据已重置成功！")
                
            except Exception as e:
                self.msg_manager.add_message(f"重置数据时发生错误: {str(e)}", "error")
                messagebox.showerror("错误", f"重置数据失败: {str(e)}")