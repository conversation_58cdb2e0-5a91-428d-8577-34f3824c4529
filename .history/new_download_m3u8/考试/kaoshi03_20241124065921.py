import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import json
import hashlib
import logging
from fuzzywuzzy import fuzz
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import tkinter.font as tkfont
import time
import re
from question_matcher import QuestionMatcher
from question_bank_manager import QuestionBankManager

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 在文件开头添加这个类
class MessageManager:
    def __init__(self, text_widget, root):
        self.text_widget = text_widget
        self.root = root
        
        # 配置文本标签样式
        self.text_widget.tag_configure("error", foreground="red")
        self.text_widget.tag_configure("warning", foreground="orange")
        self.text_widget.tag_configure("success", foreground="green")
        self.text_widget.tag_configure("info", foreground="black")
    
    def add_message(self, message, level="info"):
        try:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            
            if level == "error":
                prefix = "错误"
            elif level == "warning":
                prefix = "警告"
            elif level == "success":
                prefix = "成功"
            else:
                prefix = "信息"
            
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, f"[{timestamp}] [{prefix}] {message}\n", level)
            self.text_widget.see(tk.END)
            self.text_widget.configure(state='disabled')
            self.root.update()
        except Exception as e:
            print(f"添加消息时出错: {e}")
    
    def clear_messages(self):
        """清除所有消息"""
        try:
            self.text_widget.configure(state='normal')
            self.text_widget.delete(1.0, tk.END)
            self.text_widget.configure(state='disabled')
            self.root.update()
        except Exception as e:
            print(f"清除消息时出错: {e}")

# 读取文本文件
def read_txt_file(file_path):
    """读取文本文件，将完整题目组合在一起"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read()
                # 使用正则表达式分割题目，处理题号和换行的情况
                questions = re.split(r'\n(?=\d+、\n?)', content)
                # 清理每个题目，合并多行
                cleaned_questions = []
                for q in questions:
                    if q.strip():
                        # 移除多余的换行，保持题目格式
                        q = re.sub(r'\n+', '\n', q.strip())
                        # 确保题号和题目在同一行
                        q = re.sub(r'^(\d+、)\n', r'\1', q)
                        cleaned_questions.append(q)
                return cleaned_questions
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    """读取Word文件，将完整题目组合在一起"""
    doc = Document(file_path)
    content = '\n'.join(para.text for para in doc.paragraphs if para.text.strip())
    # 使用正则表达式分割题目
    questions = re.split(r'\n(?=\d+、)', content)
    # 清理每个题目
    questions = [q.strip() for q in questions if q.strip()]
    return questions

# 加载题和题库
def load_questions(file_path):
    """读取题目文件"""
    if file_path.endswith('.txt'):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用更复杂的正则表达式来分割题目
            questions = []
            # 首先按题号分割，同时保留题号
            raw_questions = re.split(r'(?=(?:\d+[\.、]|\(\d+\)[\.、]))', content)
            
            current_question = []
            for q in raw_questions:
                q = q.strip()
                if not q:
                    continue
                
                # 如果是新题目的开始
                if re.match(r'^\d+[\.、]|^\(\d+\)[\.、]', q):
                    if current_question:
                        # 合并之前收集的题目内容
                        full_question = '\n'.join(current_question)
                        if full_question.strip():
                            questions.append(full_question)
                        current_question = []
                    current_question.append(q)
                else:
                    # 如果不是题号开头，可能是题目的继续或选项
                    if current_question:  # 确保有正在处理的题目
                        current_question.append(q)
            
            # 添加最后一个题目
            if current_question:
                full_question = '\n'.join(current_question)
                if full_question.strip():
                    questions.append(full_question)
            
            # 清理每个题目
            cleaned_questions = []
            for q in questions:
                if q.strip():
                    # 保持题目格式，包括选项
                    cleaned = re.sub(r'\n+', '\n', q.strip())  # 合并多个换行
                    cleaned_questions.append(cleaned)
            
            print(f"从文件中读取到 {len(cleaned_questions)} 个题目")
            # 打印前几个题目用于调试
            for i, q in enumerate(cleaned_questions[:3], 1):
                print(f"\n题目 {i}:")
                print(q)
                print("-" * 50)
            
            return cleaned_questions
            
        except Exception as e:
            logging.error(f"读取文本文件时出错: {e}")
            return []
            
    elif file_path.endswith('.docx'):
        try:
            doc = Document(file_path)
            questions = []
            current_question = []
            
            for para in doc.paragraphs:
                text = para.text.strip()
                if not text:
                    continue
                
                # 如果是新题目的开始
                if re.match(r'^\d+[\.、]|^\(\d+\)[\.、]', text):
                    if current_question:
                        questions.append('\n'.join(current_question))
                        current_question = []
                    current_question.append(text)
                else:
                    # 如果是选项或题目的继续
                    if current_question or re.match(r'^[A-D]\.', text):
                        current_question.append(text)
            
            # 添加最后一个题目
            if current_question:
                questions.append('\n'.join(current_question))
            
            # 清理题目
            cleaned_questions = []
            for q in questions:
                if q.strip():
                    cleaned = re.sub(r'\n+', '\n', q.strip())
                    cleaned_questions.append(cleaned)
            
            print(f"从文件中读取到 {len(cleaned_questions)} 个题目")
            # 打印前几个题目用于调试
            for i, q in enumerate(cleaned_questions[:3], 1):
                print(f"\n题目 {i}:")
                print(q)
                print("-" * 50)
            
            return cleaned_questions
            
        except Exception as e:
            logging.error(f"读取Word文件时出错: {e}")
            return []
    
    else:
        logging.error("不支持的文件格式")
        return []

# 从JSON文件加载题库
def load_question_bank():
    """加载题库"""
    try:
        with open('question_bank.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        matcher = QuestionMatcher()
        for category, questions in data.items():
            if isinstance(questions, list):
                for question in questions:
                    if isinstance(question, dict):
                        question_text = question.get('question', '')
                        answer = question.get('answer', '')
                        if question_text and answer:
                            matcher.add_question(question_text, answer)
        return matcher
    except Exception as e:
        logging.error(f"加载题库时出错: {e}")
        return None

# 生成比较后的文档文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建四列表格
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '考试题目'
        hdr_cells[1].text = '匹配分数'
        hdr_cells[2].text = '匹配案'
        hdr_cells[3].text = '匹配'
        
        # 添加数据
        for question, answer, score in matched_answers:
            if not question.strip():
                continue
            
            row_cells = table.add_row().cells
            row_cells[0].text = question.strip()
            row_cells[1].text = f"{score}%" if score > 0 else "0%"
            row_cells[2].text = answer.strip()
            
            # 根据匹配分数确定状态
            if score >= 80:
                status = "完全匹配"
            elif score >= 60:
                status = "部分匹配"
            else:
                status = "未匹配"
            row_cells[3].text = status
        
        doc.save(output_path)
        return True
    except Exception as e:
        logging.error(f"保存文时发生错误: {e}")
        return False

# 提取题目标题部分，不包含选项和答案
def extract_title(text):
    """提取题目标题部分，不包含选项和答案"""
    # 移除多余的空白字符和换行
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 匹配题目格式：数字、题目内容
    question_match = re.match(r'^\d+、\s*(.+?)(?=\s*(?:单选题|多选题|判断题|\(\d+分\)|A\.|$))', text)
    if question_match:
        # 只返回题目内容分，并移除末尾可能的"单"、"多"等字符
        content = question_match.group(1).strip()
        content = re.sub(r'\s*[单多判]\s*$', '', content)  # 移除末尾的"单"、"多"、"判"
        logging.debug(f"提取到题目内容: {content}")
        return content
    
    # 如果没有找到标准格式，尝试提取第一个选项前的内容
    alt_match = re.split(r'\s*(?:单选题|多选题|判断题|\(\d+分\)|A\.)', text)[0]
    alt_match = re.sub(r'^\d+、\s*', '', alt_match)  # 移除题号
    alt_match = re.sub(r'\s*[单多判]\s*$', '', alt_match)  # 移除末尾的"单"、"多"、"判"
    
    if alt_match:
        logging.debug(f"使用替代方式提取题目内容: {alt_match.strip()}")
        return alt_match.strip()
    
    # 如果都败了，返回原始文本
    logging.warning(f"无法提取题目内容，使用原始文本: {text}")
    return text

# 清理文本，只保留题目内容相关的文字
def clean_text(text):
    """清理文本，只保留题目容相关的文字"""
    # 移除空白字符
    cleaned = text.strip()
    # 移除题号
    cleaned = re.sub(r'^\d+、\s*', '', cleaned)
    # 移除题和分数标记
    cleaned = re.sub(r'单选题|多选题|判断题', '', cleaned)
    cleaned = re.sub(r'\(\d+分\)', '', cleaned)
    # 移除选项部分（从A.开始的所有内容）
    cleaned = re.split(r'\s*A\.', cleaned)[0]
    # 移除特殊字符，但留中文、英文和基本标点
    cleaned = re.sub(r'[^\w\s\u4e00-\u9fff，？]', '', cleaned)
    # 替空格为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    return cleaned.strip().lower()

# 处理参考题目，创建或更新题库
def process_reference_questions(reference_file):
    """处理参考题目，创建或更新题库"""
    try:
        if not os.path.exists(reference_file):
            raise FileNotFoundError("选择的文件不存在")
            
        # 加载题目
        questions = load_questions(reference_file)
        if not questions:
            raise ValueError("未能从文件中读取到任何题目")
            
        # 创建题库管理器实例
        bank_manager = QuestionBankManager()
        
        # 处理每个题目
        success_count = 0
        total_questions = len(questions)
        
        print(f"\n开始处理题目，共有 {total_questions} 个题目")
        
        for question in questions:
            if not question.strip():
                continue
                
            # 如果是答案格式，跳过
            if re.match(r'^[A-D]\.', question.strip()):
                continue
                
            # 处理题目
            question_data = bank_manager.process_question(question)
            if question_data:
                # 添加到题库
                if bank_manager.add_question(question_data):
                    success_count += 1
                    print(f"成功添加题目: {question_data['title']}")
        
        # 保存题库
        bank_manager.save_database()
        
        msg = f"题库已更新，成功添加 {success_count} 个题目"
        logging.info(msg)
        return True, msg
        
    except Exception as e:
        error_msg = f"处理参考题目时发生错误: {str(e)}"
        logging.error(error_msg)
        return False, error_msg

# 匹配题目与答案
def match_question_to_answer(questions, question_bank):
    """匹配题目与答案"""
    try:
        if not questions:
            raise ValueError("没有要匹配的题目")
        if not question_bank:
            raise ValueError("题库为空")
            
        matched_answers = []
        total_questions = len(questions)
        
        print(f"\n开始匹配，共有 {total_questions} 个题目需要匹配")
        
        for index, question in enumerate(questions, 1):
            if not question.strip():
                continue
                
            # 如果是答案格式，跳过
            if re.match(r'^[A-D]\.', question.strip()):
                continue
                
            best_match = None
            best_score = 0
            
            # 提取题目内容
            current_title = extract_title(question)
            if not current_title:
                continue
                
            print(f"\n处理第 {index}/{total_questions} 个题目:")
            print(f"原始目: {question}")
            print(f"提取的标题: {current_title}")
            
            # 尝试配
            for q_hash, q_data in question_bank.items():
                try:
                    bank_title = q_data['title']
                    
                    # 使用多匹配方法
                    ratio_score = fuzz.ratio(current_title.lower(), bank_title.lower())
                    partial_score = fuzz.partial_ratio(current_title.lower(), bank_title.lower())
                    token_sort_score = fuzz.token_sort_ratio(current_title.lower(), bank_title.lower())
                    
                    # 取最高分数
                    score = max(ratio_score, partial_score, token_sort_score)
                    
                    if score > 80:  # 只显示高分匹
                        print(f"潜在匹配:")
                        print(f"分数: {score}%")
                        print(f"题库题目: {bank_title}")
                        print(f"题答案: {q_data['answer']}")
                    
                    if score > best_score:
                        best_score = score
                        best_match = q_data
                        
                except Exception as e:
                    print(f"计算相似度时出错: {e}")
                    continue
            
            # 使用较高的匹配阈值
            if best_match and best_score >= 80:  # 提高匹配阈值
                matched_answers.append((
                    question,
                    best_match['answer'],
                    best_score
                ))
                print(f"\n匹配成功! 分数: {best_score}%")
                print(f"匹配答案: {best_match['answer']}")
            else:
                matched_answers.append((question, "未找到匹配答案", 0))
                print(f"\n未找到合适的匹配 (高分数: {best_score}%)")
            
        return matched_answers
        
    except Exception as e:
        error_msg = f"匹配题目时发生错误: {e}"
        print(error_msg)
        logging.error(error_msg)
        raise

def create_question_hash(text):
    """创建题目内容的哈希值"""
    # 提取并清理题目内容
    content = extract_title(text)
    cleaned_content = clean_text(content)
    # 生成哈希值
    return hashlib.md5(cleaned_content.encode('utf-8')).hexdigest()

def test_single_match(msg_manager, num_questions=6):
    try:
        # 让用户选择题目文件
        questions_file = filedialog.askopenfilename(
            title='选择测试题目文件',
            filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")]
        )
        
        if not questions_file:
            msg_manager.add_message("未选择题目文件", "error")
            return "未选择题目文件"
            
        # 加载题目
        all_test_questions = load_questions(questions_file)
        if not all_test_questions:
            msg_manager.add_message("未能从文件中读取到题目", "error")
            return "未能从文件中读取到题目"
            
        # 加载并索引题库
        question_index = load_question_bank()
        if not question_index:
            msg_manager.add_message("未能加载题库", "error")
            return
            
        # 添加匹统计
        total_questions = 0
        matched_count = 0
        results = []
        
        # 添加题库内容检查
        msg_manager.add_message("\n=== 题库内容检查 ===", "info")
        if hasattr(question_index, 'questions'):
            msg_manager.add_message(f"题库中共有 {len(question_index.questions)} 个题目", "info")
            # 显示题库中的部分题目示例
            msg_manager.add_message("\n题库示例:", "info")
            for i, (hash_val, q_data) in enumerate(question_index.questions.items()):
                if i < 3:  # 只显示前3个题目
                    msg_manager.add_message(f"\n题目 {i+1}:", "info")
                    msg_manager.add_message(f"原始题目: {q_data['original']}", "info")
                    msg_manager.add_message(f"清理后: {q_data['cleaned']}", "info")
                    msg_manager.add_message(f"答案: {q_data['answer']}", "info")
        else:
            msg_manager.add_message("警告：题库格式不正确", "warning")
        
        for i, test_question in enumerate(all_test_questions, 1):
            if not test_question.strip():  # 跳过空行
                continue
                
            total_questions += 1
            msg_manager.add_message(f"\n=== 处理第 {i} 题 ===", "info")
            msg_manager.add_message(f"原始题目: {test_question}", "info")
            
            # 提取和清理题目内容
            title = extract_title(test_question)
            cleaned_title = clean_text(title)
            msg_manager.add_message(f"提取的标题: {title}", "info")
            msg_manager.add_message(f"清理后的内容: {cleaned_title}", "info")
            
            # 使用索引查找匹配
            match, score = question_index.find_match(test_question)
            
            if match and score >= 80:
                matched_count += 1
                msg_manager.add_message("\n匹配成功！", "success")
                msg_manager.add_message(f"匹配数: {score}%", "success")
                msg_manager.add_message(f"匹配题目: {match['original']}", "success")
                msg_manager.add_message(f"匹配答案: {match['answer']}", "success")
                results.append({
                    "题号": i,
                    "原始题目": test_question,
                    "最佳匹配分数": score,
                    "匹配答案": match['answer']
                })
            else:
                msg_manager.add_message("\n未找到匹配", "warning")
                msg_manager.add_message(f"最高匹配分数: {score}%", "warning")
                # 显示部最接近的题目
                msg_manager.add_message("\n最接近的题目:", "info")
                close_matches = []
                for _, q_data in question_index.questions.items():
                    temp_score = fuzz.ratio(cleaned_title, q_data['cleaned'])
                    if temp_score > 30:  # 显示相似度超过30%的题目
                        close_matches.append((q_data, temp_score))
                
                # 显示最接近的3个匹配
                for q_data, temp_score in sorted(close_matches, key=lambda x: x[1], reverse=True)[:3]:
                    msg_manager.add_message(f"\n相似度: {temp_score}%", "info")
                    msg_manager.add_message(f"题库题目: {q_data['original']}", "info")
                
                results.append({
                    "题号": i,
                    "原始题目": test_question,
                    "最佳匹配分数": 0,
                    "匹配答案": "未找到匹配"
                })
        
        # 示详细的匹配统计
        msg_manager.add_message("\n=== 匹配统计 ===", "info")
        msg_manager.add_message(f"总题目数: {total_questions}", "info")
        msg_manager.add_message(f"成功匹配: {matched_count}", "info")
        msg_manager.add_message(f"匹配失败: {total_questions - matched_count}", "info")
        msg_manager.add_message(f"匹配率: {(matched_count/total_questions*100):.2f}%", "info")
        
        # 如果一题都没匹配成功，显示详细的诊断信息
        if matched_count == 0:
            msg_manager.add_message("\n=== 匹配失败诊断 ===", "error")
            msg_manager.add_message("发现严重问题：所有题目都未能匹配成功！", "error")
            msg_manager.add_message("\n可能的原因：", "info")
            msg_manager.add_message("1. 题库加载失败或为空", "info")
            msg_manager.add_message("2. 题目格式与题库格式不一致", "info")
            msg_manager.add_message("3. 题目清理过程可能存在问题", "info")
            msg_manager.add_message("4. 匹配阈值可能设置过高", "info")
            
            msg_manager.add_message("\n建议解决方案：", "info")
            msg_manager.add_message("1. 检查题库文件是否正确加载", "info")
            msg_manager.add_message("2. 查看题目清理后的结果是否正确", "info")
            msg_manager.add_message("3. 考虑降低匹配阈值（当前80%）", "info")
            msg_manager.add_message("4. 检查题目格式是否一致", "info")
                
        return results
        
    except Exception as e:
        msg_manager.add_message(f"匹配过程出错: {str(e)}", "error")
        return None

# 新增：提取选项的函数
def extract_options(text):
    """提取题目中的选项"""
    options = []
    lines = text.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'^[A-D]\.', line):
            option = re.sub(r'^[A-D]\.', '', line).strip()
            options.append(option)
    return options

# 新：比较选项集合的相似度
def compare_options(options1, options2):
    """比较两组选项的相似度"""
    try:
        if not options1 or not options2:
            return 0
            
        if len(options1) != len(options2):
            return 0  # 选项数量不同，直接返回0
            
        # 计算选项之间的最大匹配分数
        total_score = 0
        matched_count = 0
        
        for opt1 in options1:
            best_opt_score = 0
            for opt2 in options2:
                try:
                    # 清理选项文本
                    cleaned_opt1 = clean_text(opt1)
                    cleaned_opt2 = clean_text(opt2)
                    
                    # 使用多种匹配方法
                    ratio_score = fuzz.ratio(cleaned_opt1, cleaned_opt2)
                    partial_score = fuzz.partial_ratio(cleaned_opt1, cleaned_opt2)
                    token_sort_score = fuzz.token_sort_ratio(cleaned_opt1, cleaned_opt2)
                    
                    # 取最高分数
                    score = max(ratio_score, partial_score, token_sort_score)
                    best_opt_score = max(best_opt_score, score)
                except:
                    continue
                    
            if best_opt_score >= 50:  # 只计算高于50分的匹配
                total_score += best_opt_score
                matched_count += 1
        
        # 返回平均分数，但要求至少有一半的选项匹配成功
        if matched_count >= len(options1) / 2:
            return total_score / len(options1)
        return 0
        
    except Exception as e:
        print(f"比较选项时出错: {e}")
        return 0

def adjust_column_widths():
    # 获取所有题目内容的最大长度
    max_content_width = max(len(str(q['content'])) for q in questions_data)
    
    # 设置最小和最大宽度
    min_width = 400
    max_width = 800
    
    # 计算合适的宽度（每个字符约10像素）
    content_width = min(max(min_width, max_content_width * 10), max_width)
    
    # 调整列宽
    tree.column('题目内容', width=content_width)

def show_tooltip(event):
    item = tree.identify_row(event.y)
    if item:
        # 获取鼠标所在行的题目内容
        item_values = tree.item(item)['values']
        if item_values:
            # 创建工具提示窗口
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root}+{event.y_root}")
            
            # 显示完整内容
            label = ttk.Label(tooltip, text=item_values[1], wraplength=600,
                            background="#ffffe0", relief='solid', borderwidth=1)
            label.pack()
            
            def hide_tooltip(event=None):
                tooltip.destroy()
            
            # 3秒后自动隐藏
            tooltip.after(3000, hide_tooltip)
            label.bind('<Leave>', hide_tooltip)

# 程序 (GUI 界)
def main():
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("题目与答案匹配工具")
        root.geometry("800x500")
        
        # 设置窗口样式
        style = ttk.Style()
        style.configure('TButton', font=('Arial', 10))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        
        # 创建左右分隔面板
        paned_window = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(left_frame, weight=1)
        
        # 右侧面板 - 消息区域
        right_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(right_frame, weight=2)
        
        # 右侧消息框
        message_frame = ttk.LabelFrame(right_frame, text="消息记录", padding="5")
        message_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建消息框顶部的工具栏
        toolbar_frame = ttk.Frame(message_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 添加清除按钮
        clear_button = ttk.Button(
            toolbar_frame, 
            text="清除消息", 
            command=lambda: msg_manager.clear_messages(),
            width=15
        )
        clear_button.pack(side=tk.RIGHT, padx=5)
        
        # 创建消息文本框和滚动条
        message_text = tk.Text(message_frame, wrap=tk.WORD, font=('Arial', 10))
        message_scrollbar = ttk.Scrollbar(message_frame, orient="vertical", command=message_text.yview)
        message_text.configure(yscrollcommand=message_scrollbar.set)
        
        message_text.pack(side="left", fill="both", expand=True)
        message_scrollbar.pack(side="right", fill="y")
        
        # 创建消息管理器
        msg_manager = MessageManager(message_text, root)
        
        # 定义功能函数
        def create_or_update_question_bank():
            try:
                reference_file = filedialog.askopenfilename(
                    title='选择题库文件',
                    filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")]
                )
                if not reference_file:
                    return
                    
                msg_manager.add_message(f"选择题库文件: {reference_file}")
                
                # 加载题目
                questions = load_questions(reference_file)
                if not questions:
                    msg_manager.add_message("未能从文件中读取到题目", "error")
                    return
                    
                # 创建题目分类窗口
                classify_window = tk.Toplevel(root)
                classify_window.title("题目分类")
                classify_window.geometry("1200x800")
                
                # 创建左右分隔面板
                paned = ttk.PanedWindow(classify_window, orient=tk.HORIZONTAL)
                paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                
                # 左侧：题目列表
                left_frame = ttk.Frame(paned)
                paned.add(left_frame, weight=2)
                
                # 右侧：题目详情和分类选择
                right_frame = ttk.Frame(paned)
                paned.add(right_frame, weight=1)
                
                # 创建题目列表
                columns = ('序号', '题目内容', '类型')
                tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=20)
                
                # 设置列标题和宽度
                tree.heading('序号', text='序号')
                tree.heading('题目内容', text='题目内容')
                tree.heading('类型', text='类型')
                
                tree.column('序号', width=50, minwidth=50)
                tree.column('题目内容', width=800, minwidth=400)
                tree.column('类型', width=100, minwidth=100)
                
                # 设置行高
                style = ttk.Style()
                style.configure('Treeview', rowheight=60)
                
                # 添加水平滚动条
                h_scrollbar = ttk.Scrollbar(left_frame, orient=tk.HORIZONTAL, command=tree.xview)
                tree.configure(xscrollcommand=h_scrollbar.set)
                
                # 布局
                tree.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
                h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
                message_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                
                # 右侧内容
                ttk.Label(right_frame, text="题目详情", font=('Arial', 12, 'bold')).pack(pady=5)
                
                # 题目详情文本框
                detail_text = tk.Text(right_frame, height=10, wrap=tk.WORD)
                detail_text.pack(fill=tk.X, padx=5, pady=5)
                
                # 题目类型选择
                type_frame = ttk.LabelFrame(right_frame, text="选择题目类型", padding=10)
                type_frame.pack(fill=tk.X, padx=5, pady=5)
                
                question_type = tk.StringVar(value="other")
                
                ttk.Radiobutton(type_frame, text="单选题", variable=question_type, 
                               value="single_choice").pack(anchor=tk.W)
                ttk.Radiobutton(type_frame, text="多选题", variable=question_type,
                               value="multiple_choice").pack(anchor=tk.W)
                ttk.Radiobutton(type_frame, text="判断题", variable=question_type,
                               value="judge").pack(anchor=tk.W)
                ttk.Radiobutton(type_frame, text="其他题型", variable=question_type,
                               value="other").pack(anchor=tk.W)
                
                # 存储题目数据
                questions_data = []
                
                # 填充题目列表
                for i, question in enumerate(questions, 1):
                    # 显示完整题目内容
                    item_id = tree.insert('', tk.END, values=(i, question, '待分类'))
                    questions_data.append({
                        'id': item_id,
                        'content': question,
                        'type': 'other'
                    })
                
                # 选中题目时显示详情
                def on_select(event):
                    selected_items = tree.selection()
                    if not selected_items:
                        return
                        
                    item_id = selected_items[0]
                    item_index = tree.index(item_id)
                    question_data = questions_data[item_index]
                    
                    # 显示完整题目内容
                    detail_text.delete(1.0, tk.END)
                    detail_text.insert(tk.END, question_data['content'])
                    
                    # 设置当前类型
                    question_type.set(question_data['type'])
                
                tree.bind('<<TreeviewSelect>>', on_select)
                
                # 更新题目类型
                def update_type():
                    selected_items = tree.selection()
                    if not selected_items:
                        return
                        
                    item_id = selected_items[0]
                    item_index = tree.index(item_id)
                    
                    # 更新数据
                    new_type = question_type.get()
                    questions_data[item_index]['type'] = new_type
                    
                    # 更新显示
                    values = tree.item(item_id)['values']
                    tree.item(item_id, values=(values[0], values[1], new_type))
                    
                    # 自动选择下一个题目
                    next_item = tree.next(item_id)
                    if next_item:
                        tree.selection_set(next_item)
                        tree.see(next_item)
                
                ttk.Button(right_frame, text="确认类型", command=update_type).pack(pady=10)
                
                # 保存所有题目
                def save_questions():
                    try:
                        bank_manager = QuestionBankManager()
                        success_count = 0
                        
                        for question_data in questions_data:
                            content = question_data['content']
                            q_type = question_data['type']
                            
                            if bank_manager.add_question_with_type(content, q_type):
                                success_count += 1
                        
                        bank_manager.save_database()
                        msg_manager.add_message(f"成功保存 {success_count} 个题目", "success")
                        classify_window.destroy()
                        
                    except Exception as e:
                        msg_manager.add_message(f"保存题目时发生错误: {str(e)}", "error")
                
                # 添加保存按钮
                ttk.Button(right_frame, text="保存全部题目", command=save_questions).pack(pady=10)
                
                # 批量设置类型
                def batch_set_type():
                    selected_items = tree.selection()
                    if not selected_items:
                        return
                        
                    new_type = question_type.get()
                    for item_id in selected_items:
                        item_index = tree.index(item_id)
                        questions_data[item_index]['type'] = new_type
                        values = tree.item(item_id)['values']
                        tree.item(item_id, values=(values[0], values[1], new_type))
                
                ttk.Button(right_frame, text="批量设置类型", command=batch_set_type).pack(pady=10)
                
                # 添加快捷键
                def handle_keypress(event):
                    if event.char in ['1', '2', '3', '4']:
                        type_map = {
                            '1': 'single_choice',
                            '2': 'multiple_choice',
                            '3': 'judge',
                            '4': 'other'
                        }
                        question_type.set(type_map[event.char])
                        update_type()
                
                classify_window.bind('<Key>', handle_keypress)
                
                # 在填充数据后调用
                adjust_column_widths()
                
            except Exception as e:
                msg_manager.add_message(f"创建/更新题库时发生错误: {str(e)}", "error")

        def view_question_bank():
            try:
                # 使用 QuestionBankManager 替代直接加载
                bank_manager = QuestionBankManager()
                
                # 创建新窗口显示题库内容
                view_window = tk.Toplevel(root)
                view_window.title("题库内容")
                view_window.geometry("1000x700")  # 加大窗口尺寸
                
                # 创建顶部统计信息框架
                stats_frame = ttk.LabelFrame(view_window, text="题库计", padding="5")
                stats_frame.pack(fill=tk.X, padx=5, pady=5)
                
                # 获取统计信息
                stats = bank_manager.get_statistics()
                
                # 显示统计信息
                ttk.Label(stats_frame, text=f"总题目数: {stats['total_questions']}").pack(side=tk.LEFT, padx=10)
                ttk.Label(stats_frame, text=f"单选题: {stats['by_type']['single_choice']}").pack(side=tk.LEFT, padx=10)
                ttk.Label(stats_frame, text=f"多选题: {stats['by_type']['multiple_choice']}").pack(side=tk.LEFT, padx=10)
                ttk.Label(stats_frame, text=f"判断题: {stats['by_type']['judge']}").pack(side=tk.LEFT, padx=10)
                ttk.Label(stats_frame, text=f"其他题型: {stats['by_type']['other']}").pack(side=tk.LEFT, padx=10)
                
                # 创建选项卡控件
                notebook = ttk.Notebook(view_window)
                notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                
                # 为每种题型创建选项卡
                question_types = {
                    'single_choice': '单选题',
                    'multiple_choice': '多选题',
                    'judge': '判断题',
                    'other': '其他题型'
                }
                
                for q_type, tab_name in question_types.items():
                    # 创建选项卡页面
                    tab = ttk.Frame(notebook)
                    notebook.add(tab, text=tab_name)
                    
                    # 创建表格
                    columns = ('序', '题目', '选项', '答案')
                    tree = ttk.Treeview(tab, columns=columns, show='headings')
                    
                    # 设置列标题
                    for col in columns:
                        tree.heading(col, text=col)
                        if col == '目':
                            tree.column(col, width=400, anchor='w')  # 题目列加宽
                        elif col == '选项':
                            tree.column(col, width=300, anchor='w')  # 选项列加宽
                        else:
                            tree.column(col, width=100, anchor='center')
                    
                    # 添加滚动条
                    scrollbar = ttk.Scrollbar(tab, orient=tk.VERTICAL, command=tree.yview)
                    tree.configure(yscrollcommand=scrollbar.set)
                    
                    # 布局
                    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                    
                    # 获取该类型的题目并填充数据
                    questions = bank_manager.get_questions_by_type(q_type)
                    for i, (_, question) in enumerate(questions.items(), 1):
                        # 格式化选项显示
                        options = '\n'.join(question.get('options', []))
                        tree.insert('', tk.END, values=(
                            i,
                            question.get('title', ''),
                            options,
                            question.get('answer', '')
                        ))
                
                # 添加搜索功能
                search_frame = ttk.Frame(view_window)
                search_frame.pack(fill=tk.X, padx=5, pady=5)
                
                ttk.Label(search_frame, text="搜索题目:").pack(side=tk.LEFT, padx=5)
                search_var = tk.StringVar()
                search_entry = ttk.Entry(search_frame, textvariable=search_var, width=40)
                search_entry.pack(side=tk.LEFT, padx=5)
                
                def search_questions():
                    keyword = search_var.get().strip()
                    if keyword:
                        # 获取当前选中的选项卡
                        current_tab = notebook.select()
                        current_tab_name = notebook.tab(current_tab, 'text')
                        
                        # 获取当前选项卡的类型
                        q_type = next(k for k, v in question_types.items() if v == current_tab_name)
                        
                        # 搜索题目
                        results = bank_manager.search_questions(keyword, q_type)
                        
                        # 清空当前表格
                        current_tree = notebook.select()
                        for item in current_tree.get_children():
                            current_tree.delete(item)
                        
                        # 显示搜索结果
                        for i, question in enumerate(results, 1):
                            options = '\n'.join(question.get('options', []))
                            current_tree.insert('', tk.END, values=(
                                i,
                                question.get('title', ''),
                                options,
                                question.get('answer', '')
                            ))
                
                ttk.Button(search_frame, text="搜索", command=search_questions).pack(side=tk.LEFT, padx=5)
                
                # 添加刷新按钮
                def refresh_view():
                    view_window.destroy()
                    view_question_bank()
                    
                ttk.Button(search_frame, text="刷新", command=refresh_view).pack(side=tk.RIGHT, padx=5)
                
            except Exception as e:
                msg_manager.add_message(f"查看题库时发生错误: {str(e)}", "error")

        def match_questions():
            try:
                # 直接使用 test_single_match 的逻辑
                msg_manager.add_message("开始匹配题目...", "info")
                
                # 让用户选择题目文件
                questions_file = filedialog.askopenfilename(
                    title='选择考试题目文件',
                    filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")]
                )
                
                if not questions_file:
                    msg_manager.add_message("未选择题目文件", "error")
                    return
                    
                # 加载题目
                all_test_questions = load_questions(questions_file)
                if not all_test_questions:
                    msg_manager.add_message("未能从文件中读取到题目", "error")
                    return
                    
                # 加载题库
                question_index = load_question_bank()
                if not question_index:
                    msg_manager.add_message("未能加载题库", "error")
                    return
                    
                # 处理所有题目
                results = test_single_match(msg_manager, len(all_test_questions))
                
                if isinstance(results, list) and results:
                    # 让用户选择保存位置
                    output_file = filedialog.asksaveasfilename(
                        title='保存结果文',
                        defaultextension=".docx",
                        filetypes=[("Word files", "*.docx")]
                    )
                    
                    if output_file:
                        # 创建文档
                        doc = Document()
                        title = doc.add_heading('题目与参考答案匹配结果', level=1)
                        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                        
                        # 创建表格
                        table = doc.add_table(rows=1, cols=4)
                        table.style = 'Table Grid'
                        
                        # 设置表头
                        hdr_cells = table.rows[0].cells
                        hdr_cells[0].text = '考试题目'
                        hdr_cells[1].text = '匹配分数'
                        hdr_cells[2].text = '匹配答案'
                        hdr_cells[3].text = '匹配状态'
                        
                        # 添加结果
                        matched_count = 0
                        total_questions = len(results)
                        
                        for result in results:
                            row_cells = table.add_row().cells
                            row_cells[0].text = result['原始题目']
                            row_cells[1].text = f"{result['最佳匹配分数']}%"
                            row_cells[2].text = result['匹配答案']
                            
                            if result['最佳匹配分数'] >= 80:
                                row_cells[3].text = "完全匹配"
                                matched_count += 1
                            else:
                                row_cells[3].text = "未匹配"
                        
                        # 添加统计信息
                        doc.add_paragraph("\n匹配统计:")
                        doc.add_paragraph(f"总题目数: {total_questions}")
                        doc.add_paragraph(f"成功匹配: {matched_count}")
                        doc.add_paragraph(f"匹配率: {(matched_count/total_questions*100):.2f}%")
                        
                        # 保存文档
                        doc.save(output_file)
                        msg_manager.add_message(f"结果已保存到: {output_file}", "success")
                        msg_manager.add_message(f"匹配完成！成功匹配 {matched_count}/{total_questions} 道题目", "success")
                    else:
                        msg_manager.add_message("取消保存结果", "warning")
                else:
                    msg_manager.add_message("匹配过程出错或未找到匹配结果", "error")
                    
            except Exception as e:
                msg_manager.add_message(f"匹配题目时发生错误: {str(e)}", "error")

        def close_app():
            root.destroy()

        def run_test_with_num():
            try:
                num_questions = int(test_num_var.get())
                if num_questions < 1 or num_questions > 100:
                    msg_manager.add_message("请输入1-100之间的数字", "warning")
                    return
                    
                msg_manager.add_message(f"开始测试 {num_questions} 道题目...", "info")
                results = test_single_match(msg_manager, num_questions)
                if isinstance(results, list):
                    msg_manager.add_message("\n=== 测试结果 ===", "info")
                    for result in results:
                        msg_manager.add_message(f"\n测试第 {result['题号']} 题:", "info")
                        for key, value in result.items():
                            if key != '题号':
                                msg_manager.add_message(f"{key}: {value}", "info")
                        msg_manager.add_message("-" * 50, "info")
                else:
                    msg_manager.add_message(str(results), "error")
            except ValueError:
                msg_manager.add_message("请输入有效的数字", "error")
            except Exception as e:
                msg_manager.add_message(f"测试时发生错误: {str(e)}", "error")

        # 左侧内容
        header = ttk.Label(left_frame, text="题目与答案匹配工具", style='Header.TLabel')
        header.pack(pady=(0, 20))
        
        # 创建测试题目数量输入框架
        test_frame = ttk.LabelFrame(left_frame, text="测试设置", padding="5")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建输入框和标签
        input_frame = ttk.Frame(test_frame)
        input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(input_frame, text="测试题目数量:").pack(side=tk.LEFT, padx=(0, 5))
        test_num_var = tk.StringVar(value="6")  # 默值为6
        test_num_entry = ttk.Entry(input_frame, textvariable=test_num_var, width=10)
        test_num_entry.pack(side=tk.LEFT)
        
        def validate_number(P):
            if P == "":
                return True
            try:
                num = int(P)
                return num > 0 and num <= 100  # 限制围在1-100之间
            except ValueError:
                return False
        
        vcmd = (root.register(validate_number), '%P')
        test_num_entry.configure(validate='key', validatecommand=vcmd)
        
        # 创建按钮框架
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.BOTH, expand=True)
        
        # 定义按钮样式
        button_style = {'width': 20, 'padding': 10}
        
        # 添加按钮
        ttk.Button(button_frame, text="创建/更新题库", command=create_or_update_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="查看题库", command=view_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="匹配题目", command=match_questions, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="测试指定数量题目", command=run_test_with_num, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="关闭程", command=close_app, **button_style).pack(pady=10)
        
        # 添加初始消息
        msg_manager.add_message("程序已启动，等待操作...")
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # 绑定鼠标悬停事件
        tree.bind('<Motion>', show_tooltip)
        
        root.mainloop()

    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        logging.error(f"程序运行时发生错误: {e}")

if __name__ == "__main__":
    main()


