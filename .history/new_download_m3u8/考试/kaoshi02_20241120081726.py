import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import json
import hashlib
import logging
from fuzzywuzzy import fuzz
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import tkinter.font as tkfont
import time
import re

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 在文件开头添加这个类
class MessageManager:
    def __init__(self, text_widget, root):
        self.text_widget = text_widget
        self.root = root
        
        # 配置文本标签样式
        self.text_widget.tag_configure("error", foreground="red")
        self.text_widget.tag_configure("warning", foreground="orange")
        self.text_widget.tag_configure("success", foreground="green")
        self.text_widget.tag_configure("info", foreground="black")
    
    def add_message(self, message, level="info"):
        try:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            
            if level == "error":
                prefix = "错误"
            elif level == "warning":
                prefix = "警告"
            elif level == "success":
                prefix = "成功"
            else:
                prefix = "信息"
            
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, f"[{timestamp}] [{prefix}] {message}\n", level)
            self.text_widget.see(tk.END)
            self.text_widget.configure(state='disabled')
            self.root.update()
        except Exception as e:
            print(f"添加消息时出错: {e}")

# 读取文本文件
def read_txt_file(file_path):
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return [line.strip() for line in file.readlines() if line.strip()]
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错: 不支持的文件格式，请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 从JSON文件加载题库
def load_question_bank():
    try:
        with open('question_bank.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except json.JSONDecodeError:
        return None

# 生成比较后的文档文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建四列表格
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '考试题目'
        hdr_cells[1].text = '匹配分数'
        hdr_cells[2].text = '匹配答案'
        hdr_cells[3].text = '匹配状态'
        
        # 添加数据
        for question, answer, score in matched_answers:
            if not question.strip():
                continue
            
            row_cells = table.add_row().cells
            row_cells[0].text = question.strip()
            row_cells[1].text = f"{score}%" if score > 0 else "0%"
            row_cells[2].text = answer.strip()
            
            # 根据匹配分数确定状态
            if score >= 80:
                status = "完全匹配"
            elif score >= 60:
                status = "部分匹配"
            else:
                status = "未匹配"
            row_cells[3].text = status
        
        doc.save(output_path)
        return True
    except Exception as e:
        logging.error(f"保存文件时发生错误: {e}")
        return False

# 提取题目标题部分
def extract_title(text):
    """提取题目标题部分"""
    # 移除题号
    text = re.sub(r'^[一二三四五六七八九十\d]+[.、\s)(）（]+', '', text)
    
    # 尝试找到第一个句号、问号或感叹号之前的内容作为标题
    match = re.match(r'^([^。？！\n]+)[。？！\n]', text)
    if match:
        return match.group(1).strip()
    
    # 如果没有找到标点符号，返回整行文本
    return text.strip()

# 清理文本，移除标点符号和多余空格
def clean_text(text):
    """清理文本，移除标点符号和多余空格"""
    # 移除标点符号
    text = re.sub(r'[^\w\s]', '', text)
    # 替换多个空格为单个空格
    text = re.sub(r'\s+', ' ', text)
    return text.strip().lower()

# 处理参考题目，创建或更新题库
def process_reference_questions(reference_file):
    try:
        if not os.path.exists(reference_file):
            raise FileNotFoundError("选择的文件不存在")
            
        questions = load_questions(reference_file)
        if not questions:
            raise ValueError("未能从文件中读取到任何题目")
            
        question_bank = {}
        
        for question in questions:
            if not question.strip():
                continue
                
            # 提取并清理题目标题
            title = extract_title(question)
            cleaned_title = clean_text(title)
            
            # 使用清理后的标题作为键
            question_hash = hashlib.md5(cleaned_title.encode('utf-8')).hexdigest()
            question_bank[question_hash] = {
                'question': question,  # 保存原始题目
                'title': title,        # 保存原始标题
                'cleaned_title': cleaned_title,  # 保存清理后的标题
                'type': '未分类',
                'answer': question
            }
        
        if not question_bank:
            raise ValueError("处理后的题库为空")
            
        # 保存题库到JSON文件
        with open('question_bank.json', 'w', encoding='utf-8') as f:
            json.dump(question_bank, f, ensure_ascii=False, indent=4)
            
        logging.info(f"题库已更新，共包含 {len(question_bank)} 个题目")
        return True, f"题库已更新，共包含 {len(question_bank)} 个题目"
        
    except Exception as e:
        error_msg = f"处理参考题目时发生错误: {str(e)}"
        logging.error(error_msg)
        return False, error_msg

# 匹配题目与答案
def match_question_to_answer(questions, question_bank):
    try:
        if not questions:
            raise ValueError("没有要匹配的题目")
        if not question_bank:
            raise ValueError("题库为空")
            
        matched_answers = []
        
        for question in questions:
            if not question.strip():
                continue
                
            best_match = None
            best_score = 0
            
            # 提取并清理当前题目的标题
            current_title = extract_title(question)
            cleaned_current_title = clean_text(current_title)
            
            # 打印调试信息
            logging.debug(f"正在匹配题目标题: {current_title}")
            logging.debug(f"清理后的标题: {cleaned_current_title}")
            
            for q_hash, q_data in question_bank.items():
                try:
                    bank_title = q_data['cleaned_title']
                    
                    # 使用多种匹配方法比较标题
                    ratio_score = fuzz.ratio(cleaned_current_title, bank_title)
                    partial_score = fuzz.partial_ratio(cleaned_current_title, bank_title)
                    token_sort_score = fuzz.token_sort_ratio(cleaned_current_title, bank_title)
                    token_set_score = fuzz.token_set_ratio(cleaned_current_title, bank_title)
                    
                    # 计算加权分数，增加完全匹配的权重
                    score = max(
                        ratio_score * 0.4,      # 完全匹配权重增加
                        partial_score * 0.3,     # 部分匹配权重
                        token_sort_score * 0.15,  # 词序匹配权重
                        token_set_score * 0.15    # 词集匹配权重
                    )
                    
                    # 打印每个匹配的分数
                    if score > 50:  # 只记录高于50分的匹配结果
                        logging.debug(f"与题库题目「{q_data['title']}」的匹配分数: {score}")
                    
                    if score > best_score:
                        best_score = score
                        best_match = q_data
                        
                except Exception as e:
                    logging.warning(f"计算题目相似度时发生错误: {e}")
                    continue
            
            # 降低匹配阈值到50%
            if best_match and best_score >= 50:
                matched_answers.append((
                    question,
                    best_match['answer'],
                    best_score
                ))
                logging.info(f"题目匹配成功:\n标题: {current_title}\n匹配题目: {best_match['title']}\n分数: {best_score}")
            else:
                matched_answers.append((question, "未找到匹配答案", 0))
                logging.warning(f"题目未匹配: {current_title} (最高分数: {best_score})")
            
        return matched_answers
        
    except Exception as e:
        logging.error(f"匹配题目时发生错误: {e}")
        raise

# 主程序 (GUI 界面)
def main():
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("题目与答案匹配工具")
        root.geometry("800x500")
        
        # 设置窗口样式
        style = ttk.Style()
        style.configure('TButton', font=('Arial', 10))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        
        # 创建左右分隔面板
        paned_window = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(left_frame, weight=1)
        
        # 右侧面板 - 消息区域
        right_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(right_frame, weight=2)
        
        # 右侧消息框
        message_frame = ttk.LabelFrame(right_frame, text="消息记录", padding="5")
        message_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建消息文本框和滚动条
        message_text = tk.Text(message_frame, wrap=tk.WORD, font=('Arial', 10))
        message_scrollbar = ttk.Scrollbar(message_frame, orient="vertical", command=message_text.yview)
        message_text.configure(yscrollcommand=message_scrollbar.set)
        
        message_text.pack(side="left", fill="both", expand=True)
        message_scrollbar.pack(side="right", fill="y")
        
        # 创建消息管理器
        msg_manager = MessageManager(message_text, root)
        
        # 定义功能函数
        def create_or_update_question_bank():
            try:
                reference_file = filedialog.askopenfilename(title='选择题库文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
                if reference_file:
                    msg_manager.add_message(f"选择题库文件: {reference_file}")
                    success, message = process_reference_questions(reference_file)
                    if success:
                        msg_manager.add_message(message, "success")
                    else:
                        msg_manager.add_message(message, "error")
            except Exception as e:
                msg_manager.add_message(f"创建/更新题库时发生错误: {str(e)}", "error")

        def view_question_bank():
            try:
                question_bank = load_question_bank()
                if not question_bank:
                    msg_manager.add_message("题库文件不存在或格式错误，请先创建题库", "error")
                    return
                    
                msg_manager.add_message("正在加载题库内容...")
                content = json.dumps(question_bank, ensure_ascii=False, indent=4)
                
                # 创建新窗口显示题库内容
                view_window = tk.Toplevel(root)
                view_window.title("题库内容")
                view_window.geometry("600x400")
                
                text_widget = tk.Text(view_window, wrap=tk.WORD, font=('Arial', 10))
                scrollbar = ttk.Scrollbar(view_window, orient="vertical", command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)
                
                text_widget.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")
                
                text_widget.insert("1.0", content)
                text_widget.configure(state='disabled')
                msg_manager.add_message("题库内容已加载完成", "success")
            except Exception as e:
                msg_manager.add_message(f"查看题库时发生错误: {str(e)}", "error")

        def match_questions():
            try:
                question_bank = load_question_bank()
                if not question_bank:
                    msg_manager.add_message("题库为空或无法加载，请先创建题库", "error")
                    return
                
                questions_file = filedialog.askopenfilename(title='选择考试题目文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
                if questions_file:
                    msg_manager.add_message(f"选择考试题目文件: {questions_file}")
                    questions = load_questions(questions_file)
                    if not questions:
                        msg_manager.add_message("未能读取到任何题目", "warning")
                        return
                    
                    msg_manager.add_message("正在匹配题目...")
                    matched_answers = match_question_to_answer(questions, question_bank)
                    msg_manager.add_message(f"匹配完成，共处理 {len(matched_answers)} 个题目", "success")
                    
                    output_file = filedialog.asksaveasfilename(title='保存结果文件', defaultextension=".docx", filetypes=[("Word files", "*.docx")])
                    if output_file:
                        msg_manager.add_message(f"正在保存结果到: {output_file}")
                        if generate_result_file(matched_answers, output_file):
                            msg_manager.add_message("结果文件保存成功！", "success")
                        else:
                            msg_manager.add_message("结果文件保存失败！", "error")
            except Exception as e:
                msg_manager.add_message(f"匹配题目时发生错误: {str(e)}", "error")

        def close_app():
            root.destroy()

        # 左侧内容
        header = ttk.Label(left_frame, text="题目与答案匹配工具", style='Header.TLabel')
        header.pack(pady=(0, 20))
        
        # 创建按钮框架
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.BOTH, expand=True)
        
        # 定义按钮样式
        button_style = {'width': 20, 'padding': 10}
        
        # 添加按钮
        ttk.Button(button_frame, text="创建/更新题库", command=create_or_update_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="查看题库", command=view_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="匹配题目", command=match_questions, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="关闭程序", command=close_app, **button_style).pack(pady=10)

        # 添加初始消息
        msg_manager.add_message("程序已启动，等待操作...")
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        root.mainloop()

    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        logging.error(f"程序运行时发生错误: {e}")

if __name__ == "__main__":
    main()
