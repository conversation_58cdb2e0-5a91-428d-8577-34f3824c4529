import difflib
from docx import Document
from fuzzywuzzy import fuzz

# 读取文本文件
def read_txt_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.readlines()

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题目和题库
def load_questions(file_path):
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)
    else:
        raise ValueError('Unsupported file format. Please use .txt or .docx')

# 匹配题目与参考答案
def match_question_to_answer(questions, reference_questions):
    matched_answers = []
    for question in questions:
        best_match = ""
        highest_score = 0
        for ref_question in reference_questions:
            score = fuzz.ratio(question, ref_question)
            if score > highest_score:
                highest_score = score
                best_match = ref_question
        matched_answers.append((question, best_match, highest_score))
    return matched_answers

# 生成比较后的答案文件
def generate_result_file(matched_answers, output_path):
    doc = Document()
    for question, answer, score in matched_answers:
        doc.add_paragraph(f"题目: {question}")
        doc.add_paragraph(f"匹配的参考答案: {answer}")
        doc.add_paragraph(f"匹配相似度: {score}%")
        doc.add_paragraph("\n")
    doc.save(output_path)

# 主函数
def main():
    questions_file = 'exam_questions.docx'  # 题目文件路径
    reference_file = 'reference_questions.docx'  # 题库文件路径
    output_file = 'matched_answers.docx'  # 输出文件路径

    # 加载题目和题库
    questions = load_questions(questions_file)
    reference_questions = load_questions(reference_file)

    # 比对题目和答案
    matched_answers = match_question_to_answer(questions, reference_questions)

    # 生成结果文件
    generate_result_file(matched_answers, output_file)
    print(f'比对结果已保存到 {output_file}')

if __name__ == "__main__":
    main()
