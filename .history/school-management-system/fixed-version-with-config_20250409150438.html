<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添水县第四实验小学"数字食堂"监管平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background-image: url('images/blue-wave-bg.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #0099ff; /* 作为背景图片的备用 */
        }
        
        .header {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            padding: 10px 20px;
        }
        
        .header-link {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .header-link:hover {
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .content {
            width: 100%;
            max-width: 1200px;
            padding: 20px;
            margin-top: 50px;
            text-align: center;
        }
        
        h1 {
            font-size: 36px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 80px;
            color: white;
        }
        
        .system-tiles {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            flex-wrap: wrap;
        }
        
        .tile {
            background-color: rgba(66, 133, 244, 0.7);
            padding: 20px 30px;
            border-radius: 10px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            min-width: 150px;
            z-index: 1; /* 确保系统模块在正常流中 */
        }
        
        .tile:hover {
            background-color: rgba(66, 133, 244, 0.9);
            transform: translateY(-5px);
        }
        
        .tile.disabled {
            background-color: rgba(150, 150, 150, 0.7);
            cursor: not-allowed;
        }
        
        .tile.disabled:hover {
            transform: none;
        }
        
        .login-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(47, 85, 151, 0.95);
            border-radius: 10px;
            padding: 25px;
            width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            z-index: 1000; /* 非常高的z-index确保它在最上层 */
            display: none; /* 默认隐藏 */
        }
        
        .login-panel h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 22px;
        }
        
        .role-selection {
            margin-bottom: 25px;
            max-height: 250px;
            overflow-y: auto;
        }
        
        .role-item {
            background-color: rgba(70, 130, 180, 0.5);
            padding: 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .role-item:hover {
            background-color: rgba(100, 180, 255, 0.8);
        }
        
        .role-item.selected {
            background-color: rgba(100, 180, 255, 0.8);
            position: relative;
        }
        
        .role-item.selected::after {
            content: '✓';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
        }
        
        .role-item.disabled {
            background-color: rgba(150, 150, 150, 0.5);
            color: rgba(255, 255, 255, 0.7);
            cursor: not-allowed;
        }
        
        .credentials {
            margin-bottom: 25px;
        }
        
        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            background-color: white;
            border-radius: 5px;
            padding: 8px 10px;
        }
        
        .input-group label {
            color: #333;
            flex: 0 0 60px;
        }
        
        .input-group input {
            flex: 1;
            border: none;
            padding: 8px;
            background: transparent;
            color: #333;
        }
        
        .input-group input:focus {
            outline: none;
        }
        
        .copy-btn {
            background-color: #4e8cff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .input-group .checkbox {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        .copy-selected-btn {
            background-color: #4e8cff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 14px;
            display: block;
            margin-left: auto;
        }
        
        .copy-selected-btn:hover,
        .copy-btn:hover {
            background-color: #3a7bd5;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
        }
        
        .login-btn, .open-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            background-color: #4a90e2;
        }
        
        .cancel-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            background-color: #949494;
        }
        
        .login-btn:hover, .open-btn:hover {
            background-color: #3a7bd5;
        }
        
        .cancel-btn:hover {
            background-color: #787878;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999; /* 略低于登录面板但高于其他元素 */
            display: none; /* 默认隐藏 */
        }
        
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 5px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            z-index: 2000;
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="admin-config.html" class="header-link">系统配置</a>
    </div>
    
    <div class="content">
        <h1>添水县第四实验小学"数字食堂"监管平台</h1>
        
        <div class="system-tiles" id="systemTiles">
            <!-- 系统模块将通过JavaScript动态添加 -->
        </div>
    </div>
    
    <div class="overlay" id="overlay"></div>
    
    <div class="login-panel" id="loginPanel">
        <h2 id="systemTitle">系统 - 选择身份</h2>
        
        <div class="role-selection" id="roleSelection">
            <!-- 角色将通过JavaScript动态添加 -->
        </div>
        
        <div class="credentials">
            <div class="input-group">
                <input type="checkbox" class="checkbox" data-field="schoolCode">
                <label for="schoolCode">学校编号：</label>
                <input type="text" id="schoolCode" value="011">
                <button class="copy-btn">复制</button>
            </div>
            <div class="input-group">
                <input type="checkbox" class="checkbox" data-field="username">
                <label for="username">账号：</label>
                <input type="text" id="username" value="admin1">
                <button class="copy-btn">复制</button>
            </div>
            <div class="input-group">
                <input type="checkbox" class="checkbox" data-field="password">
                <label for="password">密码：</label>
                <input type="password" id="password" value="*****">
                <button class="copy-btn">复制</button>
            </div>
            <button class="copy-selected-btn" id="copySelectedBtn">复制选中项</button>
        </div>
        
        <div class="action-buttons">
            <button class="login-btn" id="loginBtn">立即登录</button>
            <button class="open-btn" id="openWebsiteBtn">仅打开网站</button>
            <button class="cancel-btn" id="cancelBtn">取消</button>
        </div>
    </div>
    
    <div class="message" id="message"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 配置信息
            const defaultSystemsConfig = {
                food: {
                    name: '食材管理系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                finance: {
                    name: '财务管理系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                smart: {
                    name: '智慧食安系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                consumption: {
                    name: '消费系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                video: {
                    name: '天翼视联',
                    url: 'http://192.168.1.104',
                    port: '8080',
                    status: 'enabled'
                }
            };
            
            const defaultRolesConfig = {
                '管理员01': {
                    systems: ['food', 'finance', 'smart', 'consumption', 'video'],
                    status: 'enabled'
                },
                '管理员04': {
                    systems: ['food', 'finance'],
                    status: 'enabled'
                },
                '采购员02': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员03': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员05': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员06': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员07': {
                    systems: ['food'],
                    status: 'enabled'
                }
            };
            
            // 当前选中的系统和角色
            let currentSystem = '';
            let currentRole = '';
            
            // 获取DOM元素
            const loginPanel = document.getElementById('loginPanel');
            const overlay = document.getElementById('overlay');
            const systemTiles = document.getElementById('systemTiles');
            const roleSelection = document.getElementById('roleSelection');
            const systemTitle = document.getElementById('systemTitle');
            const cancelBtn = document.getElementById('cancelBtn');
            const openWebsiteBtn = document.getElementById('openWebsiteBtn');
            const loginBtn = document.getElementById('loginBtn');
            const copyButtons = document.querySelectorAll('.copy-btn');
            const copySelectedBtn = document.getElementById('copySelectedBtn');
            const checkboxes = document.querySelectorAll('.checkbox');
            const message = document.getElementById('message');
            
            // 从本地存储加载配置或使用默认配置
            function loadConfig() {
                let config = {
                    systems: { ...defaultSystemsConfig },
                    roles: { ...defaultRolesConfig }
                };
                
                // 尝试从本地存储或会话存储加载
                const savedConfig = localStorage.getItem('systemConfig') || sessionStorage.getItem('systemConfig');
                
                if (savedConfig) {
                    try {
                        const parsedConfig = JSON.parse(savedConfig);
                        
                        // 合并系统配置，保留默认名称
                        if (parsedConfig.systems) {
                            for (const systemId in parsedConfig.systems) {
                                if (config.systems[systemId]) {
                                    config.systems[systemId] = {
                                        ...config.systems[systemId],
                                        ...parsedConfig.systems[systemId]
                                    };
                                }
                            }
                        }
                        
                        // 合并角色配置
                        if (parsedConfig.roles) {
                            // 处理角色ID格式的转换
                            for (const roleId in parsedConfig.roles) {
                                const roleName = roleId.replace('admin', '管理员').replace('purchaser', '采购员');
                                if (config.roles[roleName]) {
                                    config.roles[roleName] = {
                                        ...config.roles[roleName],
                                        ...parsedConfig.roles[roleId]
                                    };
                                }
                            }
                        }
                    } catch (error) {
                        console.error('加载配置时出错:', error);
                    }
                }
                
                return config;
            }
            
            // 加载配置
            const config = loadConfig();
            
            // 渲染系统模块
            function renderSystemTiles() {
                systemTiles.innerHTML = '';
                
                for (const systemId in config.systems) {
                    const system = config.systems[systemId];
                    
                    // 创建系统模块元素
                    const tile = document.createElement('div');
                    tile.className = `tile ${system.status === 'disabled' ? 'disabled' : ''}`;
                    tile.setAttribute('data-system', systemId);
                    tile.textContent = system.name;
                    
                    // 仅对启用的系统添加点击事件
                    if (system.status !== 'disabled') {
                        tile.addEventListener('click', function() {
                            currentSystem = systemId;
                            systemTitle.textContent = system.name + ' - 选择身份';
                            
                            // 渲染可访问该系统的角色
                            renderRoles(systemId);
                            
                            // 显示登录面板和遮罩
                            loginPanel.style.display = 'block';
                            overlay.style.display = 'block';
                        });
                    }
                    
                    systemTiles.appendChild(tile);
                }
            }
            
            // 渲染角色选择
            function renderRoles(systemId) {
                roleSelection.innerHTML = '';
                
                for (const roleName in config.roles) {
                    const role = config.roles[roleName];
                    
                    // 检查该角色是否可以访问当前系统
                    const canAccessSystem = role.systems && role.systems.includes(systemId);
                    const isRoleEnabled = role.status === 'enabled';
                    
                    // 创建角色项元素
                    const roleItem = document.createElement('div');
                    roleItem.className = `role-item ${!canAccessSystem || !isRoleEnabled ? 'disabled' : ''}`;
                    roleItem.setAttribute('data-role', roleName);
                    roleItem.textContent = roleName;
                    
                    // 仅对可访问系统且启用的角色添加点击事件
                    if (canAccessSystem && isRoleEnabled) {
                        roleItem.addEventListener('click', function() {
                            // 移除其他角色的选中状态
                            document.querySelectorAll('.role-item').forEach(item => {
                                item.classList.remove('selected');
                            });
                            
                            // 添加选中状态
                            this.classList.add('selected');
                            currentRole = roleName;
                            
                            // 更新用户名
                            const username = roleName.toLowerCase().replace(/[^a-z0-9]/g, '') + '1';
                            document.getElementById('username').value = username;
                        });
                    }
                    
                    roleSelection.appendChild(roleItem);
                }
                
                // 默认选中第一个可用角色
                const firstEnabledRole = document.querySelector('.role-item:not(.disabled)');
                if (firstEnabledRole) {
                    firstEnabledRole.classList.add('selected');
                    currentRole = firstEnabledRole.getAttribute('data-role');
                    
                    // 更新用户名
                    const username = currentRole.toLowerCase().replace(/[^a-z0-9]/g, '') + '1';
                    document.getElementById('username').value = username;
                }
            }
            
            // 复制功能
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.previousElementSibling;
                    input.select();
                    document.execCommand('copy');
                    
                    // 显示反馈
                    const originalText = this.textContent;
                    this.textContent = '已复制';
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 1000);
                });
            });
            
            // 复制选中项功能
            copySelectedBtn.addEventListener('click', function() {
                const selectedFields = [];
                
                // 获取所有选中的字段值
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const fieldName = checkbox.getAttribute('data-field');
                        const fieldValue = document.getElementById(fieldName).value;
                        selectedFields.push(fieldValue);
                    }
                });
                
                // 如果有选中的字段，则复制到剪贴板
                if (selectedFields.length > 0) {
                    const textToCopy = selectedFields.join('\n');
                    
                    // 创建临时文本区域用于复制
                    const textarea = document.createElement('textarea');
                    textarea.value = textToCopy;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    
                    showMessage(`已复制 ${selectedFields.length} 个字段`);
                } else {
                    showMessage('请先选择要复制的字段');
                }
            });
            
            // 获取系统URL
            function getSystemUrl(systemId) {
                const system = config.systems[systemId];
                if (!system) return '';
                
                let url = system.url;
                // 如果URL不包含协议，添加http://
                if (url && !/^https?:\/\//i.test(url)) {
                    url = 'http://' + url;
                }
                
                // 添加端口号（如果有）
                if (system.port && system.port.trim() !== '') {
                    // 检查URL是否已经包含端口号
                    if (url.indexOf(':' + system.port) === -1) {
                        // 查找URL中的主机部分结束位置
                        const hostEnd = url.indexOf('/', 8); // 跳过 http:// 或 https://
                        if (hostEnd === -1) {
                            // 如果URL没有路径部分，直接添加端口
                            url += ':' + system.port;
                        } else {
                            // 在主机部分后添加端口
                            url = url.substring(0, hostEnd) + ':' + system.port + url.substring(hostEnd);
                        }
                    }
                }
                
                return url;
            }
            
            // 登录按钮
            loginBtn.addEventListener('click', function() {
                const schoolCode = document.getElementById('schoolCode').value;
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!schoolCode || !username || !password) {
                    showMessage('请输入学校编号、账号和密码');
                    return;
                }
                
                const url = getSystemUrl(currentSystem);
                if (!url) {
                    showMessage('系统URL配置错误');
                    return;
                }
                
                // 保存选中的登录数据到 localStorage
                const selectedData = {};
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const fieldName = checkbox.getAttribute('data-field');
                        const fieldValue = document.getElementById(fieldName).value;
                        selectedData[fieldName] = fieldValue;
                    }
                });
                
                if (Object.keys(selectedData).length > 0) {
                    localStorage.setItem('autofill_data', JSON.stringify(selectedData));
                }
                
                showMessage(`正在登录到 ${url}`);
                
                // 实际跳转到系统URL
                setTimeout(() => {
                    window.location.href = url;
                }, 1000);
            });
            
            // 仅打开网站按钮
            openWebsiteBtn.addEventListener('click', function() {
                const url = getSystemUrl(currentSystem);
                if (!url) {
                    showMessage('系统URL配置错误');
                    return;
                }
                
                showMessage(`正在打开 ${url}`);
                
                // 在新窗口打开系统URL
                window.open(url, '_blank');
                
                // 关闭登录面板
                loginPanel.style.display = 'none';
                overlay.style.display = 'none';
            });
            
            // 取消按钮
            cancelBtn.addEventListener('click', function() {
                loginPanel.style.display = 'none';
                overlay.style.display = 'none';
            });
            
            // 点击遮罩也可以关闭登录面板
            overlay.addEventListener('click', function() {
                loginPanel.style.display = 'none';
                overlay.style.display = 'none';
            });
            
            // 显示消息
            function showMessage(text) {
                message.textContent = text;
                message.style.display = 'block';
                
                setTimeout(() => {
                    message.style.display = 'none';
                }, 3000);
            }
            
            // 初始化渲染
            renderSystemTiles();
        });
    </script>
</body>
</html>