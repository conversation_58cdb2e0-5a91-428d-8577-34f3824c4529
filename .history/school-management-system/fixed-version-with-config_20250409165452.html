<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添水县第四实验小学"数字食堂"监管平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background-image: url('images/blue-wave-bg.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #0099ff; /* 作为背景图片的备用 */
        }
        
        .header {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            padding: 10px 20px;
        }
        
        .header-link {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .header-link:hover {
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .content {
            width: 100%;
            max-width: 1200px;
            padding: 20px;
            margin-top: 50px;
            text-align: center;
        }
        
        h1 {
            font-size: 36px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 80px;
            color: white;
        }
        
        .system-tiles {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
            flex-wrap: wrap;
        }
        
        .tile {
            background-color: rgba(66, 133, 244, 0.7);
            padding: 20px 30px;
            border-radius: 10px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            min-width: 150px;
            z-index: 1; /* 确保系统模块在正常流中 */
        }
        
        .tile:hover {
            background-color: rgba(66, 133, 244, 0.9);
            transform: translateY(-5px);
        }
        
        .tile.disabled {
            background-color: rgba(150, 150, 150, 0.7);
            cursor: not-allowed;
        }
        
        .tile.disabled:hover {
            transform: none;
        }
        
        .login-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(47, 85, 151, 0.95);
            border-radius: 10px;
            padding: 25px;
            width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            z-index: 1000; /* 非常高的z-index确保它在最上层 */
            display: none; /* 默认隐藏 */
        }
        
        .login-panel h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 22px;
        }
        
        .role-selection {
            margin-bottom: 25px;
            max-height: 250px;
            overflow-y: auto;
        }
        
        .role-item {
            background-color: rgba(70, 130, 180, 0.5);
            padding: 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .role-item:hover {
            background-color: rgba(100, 180, 255, 0.8);
        }
        
        .role-item.selected {
            background-color: rgba(100, 180, 255, 0.8);
            position: relative;
        }
        
        .role-item.selected::after {
            content: '✓';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
        }
        
        .role-item.disabled {
            background-color: rgba(150, 150, 150, 0.5);
            color: rgba(255, 255, 255, 0.7);
            cursor: not-allowed;
        }
        
        /* 用户信息显示在角色选择中 */
        .user-info-display {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 12px;
            margin: 15px 0;
            color: #333;
        }
        
        .user-info-display h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            color: #4a90e2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info-display h3 .edit-btn {
            font-size: 12px;
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .user-info-display h3 .export-btn {
            font-size: 12px;
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        .user-info-display .info-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .user-info-display .info-label {
            flex: 0 0 60px;
            font-weight: bold;
        }
        
        .user-info-display .info-value {
            flex: 1;
        }
        
        .user-info-display .info-value:empty:before {
            content: "未填写";
            color: #999;
            font-style: italic;
            font-size: 13px;
        }
        
        .user-info-display .info-value.has-value {
            color: #333;
            font-weight: 500;
        }
        
        .info-edit-form {
            display: none;
            margin-top: 10px;
        }
        
        .info-edit-form .form-group {
            margin-bottom: 8px;
        }
        
        .info-edit-form label {
            display: block;
            font-size: 12px;
            margin-bottom: 3px;
        }
        
        .info-edit-form input {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 13px;
        }
        
        .info-edit-form .button-group {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .info-edit-form button {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            color: white;
        }
        
        .info-edit-form .save-btn {
            background-color: #4a90e2;
        }
        
        .info-edit-form .cancel-btn {
            background-color: #949494;
        }
        
        .credentials {
            margin-bottom: 25px;
        }
        
        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            background-color: white;
            border-radius: 5px;
            padding: 8px 10px;
        }
        
        .input-group label {
            color: #333;
            flex: 0 0 60px;
        }
        
        .input-group input {
            flex: 1;
            border: none;
            padding: 8px;
            background: transparent;
            color: #333;
        }
        
        .input-group input:focus {
            outline: none;
        }
        
        .copy-btn {
            background-color: #4e8cff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .input-group .checkbox {
            margin-right: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }
        
        .copy-selected-btn {
            background-color: #4e8cff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 14px;
            display: block;
            margin-left: auto;
        }
        
        .copy-selected-btn:hover,
        .copy-btn:hover {
            background-color: #3a7bd5;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
        }
        
        .login-btn, .open-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            background-color: #4a90e2;
        }
        
        .cancel-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            background-color: #949494;
        }
        
        .login-btn:hover, .open-btn:hover {
            background-color: #3a7bd5;
        }
        
        .cancel-btn:hover {
            background-color: #787878;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999; /* 略低于登录面板但高于其他元素 */
            display: none; /* 默认隐藏 */
        }
        
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 5px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            z-index: 2000;
            display: none;
        }
        
        /* 用户信息表单样式 */
        .user-info-form {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }
        
        .user-info-form h3 {
            color: #4a90e2;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .user-info-form .form-group {
            margin-bottom: 10px;
        }
        
        .user-info-form label {
            display: block;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .user-info-form input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .user-info-form .buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .user-info-form button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: white;
        }
        
        .user-info-form .save-btn {
            background-color: #4a90e2;
        }
        
        .user-info-form .cancel-btn {
            background-color: #949494;
        }
        
        .show-user-info-btn {
            background-color: #4e8cff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        
        .add-user-info-btn {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 15px 0;
            display: none; /* 默认隐藏，只在需要时显示 */
        }
        
        .add-user-info-btn:hover {
            background-color: #3a7bd5;
        }
        
        .load-user-info-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
        }
        
        .load-user-info-btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="admin-config.html" class="header-link">系统配置</a>
    </div>
    
    <div class="content">
        <h1>添水县第四实验小学"数字食堂"监管平台</h1>
        
        <div class="system-tiles" id="systemTiles">
            <!-- 系统模块将通过JavaScript动态添加 -->
        </div>
    </div>
    
    <div class="overlay" id="overlay"></div>
    
    <div class="login-panel" id="loginPanel">
        <h2 id="systemTitle">系统 - 选择身份</h2>
        
        <div class="role-selection" id="roleSelection">
            <!-- 角色将通过JavaScript动态添加 -->
        </div>
        
        <!-- 用户信息显示 -->
        <div class="user-info-display" id="userInfoDisplay">
            <h3>用户信息 
                <div>
                    <button class="export-btn" id="exportUserInfoBtn">导出</button>
                    <button class="edit-btn" id="editUserInfoBtn">编辑</button>
                </div>
            </h3>
            <div class="info-item">
                <div class="info-label">号码:</div>
                <div class="info-value" id="displayNumber"></div>
            </div>
            <div class="info-item">
                <div class="info-label">地址:</div>
                <div class="info-value" id="displayAddress"></div>
            </div>
            <div class="info-item">
                <div class="info-label">部门:</div>
                <div class="info-value" id="displayDepartment"></div>
            </div>
            <div class="info-item">
                <div class="info-label">职位:</div>
                <div class="info-value" id="displayPosition"></div>
            </div>
            
            <!-- 编辑表单 -->
            <div class="info-edit-form" id="infoEditForm">
                <div class="form-group">
                    <label for="editNumber">号码:</label>
                    <input type="text" id="editNumber" placeholder="请输入号码">
                </div>
                <div class="form-group">
                    <label for="editAddress">地址:</label>
                    <input type="text" id="editAddress" placeholder="请输入地址">
                </div>
                <div class="form-group">
                    <label for="editDepartment">部门:</label>
                    <input type="text" id="editDepartment" placeholder="请输入部门">
                </div>
                <div class="form-group">
                    <label for="editPosition">职位:</label>
                    <input type="text" id="editPosition" placeholder="请输入职位">
                </div>
                <div class="button-group">
                    <button class="save-btn" id="saveInfoBtn">保存</button>
                    <button class="cancel-btn" id="cancelEditBtn">取消</button>
                </div>
            </div>
        </div>
        
        <!-- 添加用户信息按钮 -->
        <button class="add-user-info-btn" id="addUserInfoBtn">添加用户信息</button>
        
        <div class="credentials">
            <div class="input-group">
                <input type="checkbox" class="checkbox" data-field="schoolCode">
                <label for="schoolCode">学校编号：</label>
                <input type="text" id="schoolCode" value="011">
                <button class="copy-btn">复制</button>
            </div>
            <div class="input-group">
                <input type="checkbox" class="checkbox" data-field="username">
                <label for="username">账号：</label>
                <input type="text" id="username" value="admin1">
                <button class="copy-btn">复制</button>
                <button class="load-user-info-btn" id="loadUserInfoBtn">载入</button>
            </div>
            <div class="input-group">
                <input type="checkbox" class="checkbox" data-field="password">
                <label for="password">密码：</label>
                <input type="password" id="password" value="*****">
                <button class="copy-btn">复制</button>
            </div>
            <button class="copy-selected-btn" id="copySelectedBtn">复制选中项</button>
        </div>
        
        <!-- 用户信息表单 -->
        <div class="user-info-form" id="userInfoForm">
            <h3>用户信息</h3>
            <div class="form-group">
                <label for="userNumber">号码:</label>
                <input type="text" id="userNumber" placeholder="请输入号码">
            </div>
            <div class="form-group">
                <label for="userAddress">地址:</label>
                <input type="text" id="userAddress" placeholder="请输入地址">
            </div>
            <div class="form-group">
                <label for="userDepartment">部门:</label>
                <input type="text" id="userDepartment" placeholder="请输入部门">
            </div>
            <div class="form-group">
                <label for="userPosition">职位:</label>
                <input type="text" id="userPosition" placeholder="请输入职位">
            </div>
            <div class="buttons">
                <button class="save-btn" id="saveUserInfoBtn">保存为JSON</button>
                <button class="cancel-btn" id="cancelUserInfoBtn">取消</button>
            </div>
        </div>
        
        <button class="show-user-info-btn" id="showUserInfoBtn">填写用户信息</button>
        
        <div class="action-buttons">
            <button class="login-btn" id="loginBtn">立即登录</button>
            <button class="open-btn" id="openWebsiteBtn">仅打开网站</button>
            <button class="cancel-btn" id="cancelBtn">取消</button>
        </div>
    </div>
    
    <div class="message" id="message"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 配置信息
            const defaultSystemsConfig = {
                food: {
                    name: '食材管理系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                finance: {
                    name: '财务管理系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                smart: {
                    name: '智慧食安系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                consumption: {
                    name: '消费系统',
                    url: 'http://*************',
                    port: '8080',
                    status: 'enabled'
                },
                video: {
                    name: '天翼视联',
                    url: 'http://192.168.1.104',
                    port: '8080',
                    status: 'enabled'
                }
            };
            
            const defaultRolesConfig = {
                '管理员01': {
                    systems: ['food', 'finance', 'smart', 'consumption', 'video'],
                    status: 'enabled'
                },
                '管理员04': {
                    systems: ['food', 'finance'],
                    status: 'enabled'
                },
                '采购员02': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员03': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员05': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员06': {
                    systems: ['food'],
                    status: 'enabled'
                },
                '采购员07': {
                    systems: ['food'],
                    status: 'enabled'
                }
            };
            
            // 当前选中的系统和角色
            let currentSystem = '';
            let currentRole = '';
            
            // 获取DOM元素
            const loginPanel = document.getElementById('loginPanel');
            const overlay = document.getElementById('overlay');
            const systemTiles = document.getElementById('systemTiles');
            const roleSelection = document.getElementById('roleSelection');
            const systemTitle = document.getElementById('systemTitle');
            const cancelBtn = document.getElementById('cancelBtn');
            const openWebsiteBtn = document.getElementById('openWebsiteBtn');
            const loginBtn = document.getElementById('loginBtn');
            const copyButtons = document.querySelectorAll('.copy-btn');
            const copySelectedBtn = document.getElementById('copySelectedBtn');
            const checkboxes = document.querySelectorAll('.checkbox');
            const message = document.getElementById('message');
            const userInfoForm = document.getElementById('userInfoForm');
            const showUserInfoBtn = document.getElementById('showUserInfoBtn');
            const saveUserInfoBtn = document.getElementById('saveUserInfoBtn');
            const cancelUserInfoBtn = document.getElementById('cancelUserInfoBtn');
            
            // 用户信息显示和编辑相关元素
            const userInfoDisplay = document.getElementById('userInfoDisplay');
            const editUserInfoBtn = document.getElementById('editUserInfoBtn');
            const exportUserInfoBtn = document.getElementById('exportUserInfoBtn');
            const infoEditForm = document.getElementById('infoEditForm');
            const saveInfoBtn = document.getElementById('saveInfoBtn');
            const cancelEditBtn = document.getElementById('cancelEditBtn');
            const addUserInfoBtn = document.getElementById('addUserInfoBtn');
            
            // 显示字段
            const displayNumber = document.getElementById('displayNumber');
            const displayAddress = document.getElementById('displayAddress');
            const displayDepartment = document.getElementById('displayDepartment');
            const displayPosition = document.getElementById('displayPosition');
            
            // 编辑字段
            const editNumber = document.getElementById('editNumber');
            const editAddress = document.getElementById('editAddress');
            const editDepartment = document.getElementById('editDepartment');
            const editPosition = document.getElementById('editPosition');
            
            // 从本地存储加载配置或使用默认配置
            function loadConfig() {
                let config = {
                    systems: { ...defaultSystemsConfig },
                    roles: { ...defaultRolesConfig }
                };
                
                // 尝试从本地存储或会话存储加载
                const savedConfig = localStorage.getItem('systemConfig') || sessionStorage.getItem('systemConfig');
                
                if (savedConfig) {
                    try {
                        const parsedConfig = JSON.parse(savedConfig);
                        
                        // 合并系统配置，保留默认名称
                        if (parsedConfig.systems) {
                            for (const systemId in parsedConfig.systems) {
                                if (config.systems[systemId]) {
                                    config.systems[systemId] = {
                                        ...config.systems[systemId],
                                        ...parsedConfig.systems[systemId]
                                    };
                                }
                            }
                        }
                        
                        // 合并角色配置
                        if (parsedConfig.roles) {
                            // 处理角色ID格式的转换
                            for (const roleId in parsedConfig.roles) {
                                const roleName = roleId.replace('admin', '管理员').replace('purchaser', '采购员');
                                if (config.roles[roleName]) {
                                    config.roles[roleName] = {
                                        ...config.roles[roleName],
                                        ...parsedConfig.roles[roleId]
                                    };
                                }
                            }
                        }
                    } catch (error) {
                        console.error('加载配置时出错:', error);
                    }
                }
                
                return config;
            }
            
            // 加载配置
            const config = loadConfig();
            
            // 渲染系统模块
            function renderSystemTiles() {
                systemTiles.innerHTML = '';
                
                for (const systemId in config.systems) {
                    const system = config.systems[systemId];
                    
                    // 创建系统模块元素
                    const tile = document.createElement('div');
                    tile.className = `tile ${system.status === 'disabled' ? 'disabled' : ''}`;
                    tile.setAttribute('data-system', systemId);
                    tile.textContent = system.name;
                    
                    // 仅对启用的系统添加点击事件
                    if (system.status !== 'disabled') {
                        tile.addEventListener('click', function() {
                            currentSystem = systemId;
                            systemTitle.textContent = system.name + ' - 选择身份';
                            
                            // 渲染可访问该系统的角色
                            renderRoles(systemId);
                            
                            // 显示登录面板和遮罩
                            loginPanel.style.display = 'block';
                            overlay.style.display = 'block';
                        });
                    }
                    
                    systemTiles.appendChild(tile);
                }
            }
            
            // 渲染角色选择
            function renderRoles(systemId) {
                roleSelection.innerHTML = '';
                
                for (const roleName in config.roles) {
                    const role = config.roles[roleName];
                    
                    // 检查该角色是否可以访问当前系统
                    const canAccessSystem = role.systems && role.systems.includes(systemId);
                    const isRoleEnabled = role.status === 'enabled';
                    
                    // 创建角色项元素
                    const roleItem = document.createElement('div');
                    roleItem.className = `role-item ${!canAccessSystem || !isRoleEnabled ? 'disabled' : ''}`;
                    roleItem.setAttribute('data-role', roleName);
                    roleItem.textContent = roleName;
                    
                    // 仅对可访问系统且启用的角色添加点击事件
                    if (canAccessSystem && isRoleEnabled) {
                        roleItem.addEventListener('click', function() {
                            // 移除其他角色的选中状态
                            document.querySelectorAll('.role-item').forEach(item => {
                                item.classList.remove('selected');
                            });
                            
                            // 添加选中状态
                            this.classList.add('selected');
                            currentRole = roleName;
                            
                            // 更新用户名
                            const username = roleName.toLowerCase().replace(/[^a-z0-9]/g, '') + '1';
                            document.getElementById('username').value = username;
                        });
                    }
                    
                    roleSelection.appendChild(roleItem);
                }
                
                // 默认选中第一个可用角色
                const firstEnabledRole = document.querySelector('.role-item:not(.disabled)');
                if (firstEnabledRole) {
                    firstEnabledRole.classList.add('selected');
                    currentRole = firstEnabledRole.getAttribute('data-role');
                    
                    // 更新用户名
                    const username = currentRole.toLowerCase().replace(/[^a-z0-9]/g, '') + '1';
                    document.getElementById('username').value = username;
                }
            }
            
            // 复制功能
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.previousElementSibling;
                    input.select();
                    document.execCommand('copy');
                    
                    // 显示反馈
                    const originalText = this.textContent;
                    this.textContent = '已复制';
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 1000);
                });
            });
            
            // 复制选中项功能
            copySelectedBtn.addEventListener('click', function() {
                const selectedFields = [];
                
                // 获取所有选中的字段值
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const fieldName = checkbox.getAttribute('data-field');
                        const fieldValue = document.getElementById(fieldName).value;
                        selectedFields.push(fieldValue);
                    }
                });
                
                // 如果有选中的字段，则复制到剪贴板
                if (selectedFields.length > 0) {
                    const textToCopy = selectedFields.join('\n');
                    
                    // 创建临时文本区域用于复制
                    const textarea = document.createElement('textarea');
                    textarea.value = textToCopy;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    
                    showMessage(`已复制 ${selectedFields.length} 个字段`);
                } else {
                    showMessage('请先选择要复制的字段');
                }
            });
            
            // 获取系统URL
            function getSystemUrl(systemId) {
                const system = config.systems[systemId];
                if (!system) return '';
                
                let url = system.url;
                // 如果URL不包含协议，添加http://
                if (url && !/^https?:\/\//i.test(url)) {
                    url = 'http://' + url;
                }
                
                // 添加端口号（如果有）
                if (system.port && system.port.trim() !== '') {
                    // 检查URL是否已经包含端口号
                    if (url.indexOf(':' + system.port) === -1) {
                        // 查找URL中的主机部分结束位置
                        const hostEnd = url.indexOf('/', 8); // 跳过 http:// 或 https://
                        if (hostEnd === -1) {
                            // 如果URL没有路径部分，直接添加端口
                            url += ':' + system.port;
                        } else {
                            // 在主机部分后添加端口
                            url = url.substring(0, hostEnd) + ':' + system.port + url.substring(hostEnd);
                        }
                    }
                }
                
                return url;
            }
            
            // 登录按钮
            loginBtn.addEventListener('click', function() {
                const schoolCode = document.getElementById('schoolCode').value;
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!schoolCode || !username || !password) {
                    showMessage('请输入学校编号、账号和密码');
                    return;
                }
                
                const url = getSystemUrl(currentSystem);
                if (!url) {
                    showMessage('系统URL配置错误');
                    return;
                }
                
                // 保存选中的登录数据到 localStorage
                const selectedData = {};
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const fieldName = checkbox.getAttribute('data-field');
                        const fieldValue = document.getElementById(fieldName).value;
                        selectedData[fieldName] = fieldValue;
                    }
                });
                
                // 创建包含自动填充代码的弹出窗口
                if (Object.keys(selectedData).length > 0) {
                    // 将数据转换为字符串以便在打开的窗口中使用
                    const dataStr = JSON.stringify(selectedData);
                    
                    // 创建包含自动填充代码的弹出窗口
                    const popup = window.open(url, '_blank');
                    
                    // 在弹出窗口加载后注入自动填充代码
                    setTimeout(() => {
                        try {
                            // 确保弹出窗口存在并已加载
                            if (popup && !popup.closed) {
                                // 创建一个函数来进行自动填充
                                const fillFormScript = `
                                (function() {
                                    // 登录数据
                                    const loginData = ${dataStr};
                                    
                                    // 主自动填充函数
                                    function fillLoginForm() {
                                        console.log("尝试自动填充登录表单", loginData);
                                        
                                        // 检测并填充表单
                                        const allInputs = document.querySelectorAll('input[type="text"], input[type="password"], input[type="tel"], input[type="email"], input:not([type])');
                                        if (allInputs.length === 0) {
                                            console.log("未找到表单输入框，将在500ms后重试");
                                            return false;
                                        }
                                        
                                        // 填充学校编号（第一个文本字段）
                                        if (loginData.schoolCode) {
                                            for (let i = 0; i < allInputs.length; i++) {
                                                const input = allInputs[i];
                                                if (input.type !== 'password') {
                                                    if (input.placeholder && (
                                                        input.placeholder.includes('学校') || 
                                                        input.placeholder.includes('编号') || 
                                                        input.placeholder.includes('编码') ||
                                                        input.placeholder.includes('代码')
                                                    )) {
                                                        input.value = loginData.schoolCode;
                                                        input.dispatchEvent(new Event('input', { bubbles: true }));
                                                        console.log("已填充学校编号到:", input);
                                                        break;
                                                    } else if (i === 0) { // 如果没有匹配的 placeholder，使用第一个非密码字段
                                                        input.value = loginData.schoolCode;
                                                        input.dispatchEvent(new Event('input', { bubbles: true }));
                                                        console.log("已填充学校编号到第一个输入框:", input);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        
                                        // 填充用户名（第二个文本字段或匹配 placeholder 的字段）
                                        if (loginData.username) {
                                            let textFieldCount = 0;
                                            let foundField = false;
                                            
                                            for (let i = 0; i < allInputs.length; i++) {
                                                const input = allInputs[i];
                                                if (input.type !== 'password') {
                                                    textFieldCount++;
                                                    
                                                    if (textFieldCount === 2 || 
                                                        (input.placeholder && (
                                                            input.placeholder.includes('用户') || 
                                                            input.placeholder.includes('账号') ||
                                                            input.placeholder.includes('账户') ||
                                                            input.placeholder.includes('名称')
                                                        )) ||
                                                        (input.name && (
                                                            input.name.includes('user') || 
                                                            input.name.includes('name') ||
                                                            input.name.includes('account')
                                                        ))
                                                    ) {
                                                        input.value = loginData.username;
                                                        input.dispatchEvent(new Event('input', { bubbles: true }));
                                                        console.log("已填充用户名到:", input);
                                                        foundField = true;
                                                        break;
                                                    }
                                                }
                                            }
                                            
                                            // 如果没有找到第二个文本字段，尝试寻找其他可能的字段
                                            if (!foundField) {
                                                for (let i = 0; i < allInputs.length; i++) {
                                                    const input = allInputs[i];
                                                    if (input.type !== 'password' && !input.value) {
                                                        input.value = loginData.username;
                                                        input.dispatchEvent(new Event('input', { bubbles: true }));
                                                        console.log("已填充用户名到可用字段:", input);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        
                                        // 填充密码
                                        if (loginData.password) {
                                            const passwordFields = Array.from(allInputs).filter(input => 
                                                input.type === 'password' || 
                                                (input.name && input.name.includes('pass')) ||
                                                (input.placeholder && input.placeholder.includes('密码'))
                                            );
                                            
                                            if (passwordFields.length > 0) {
                                                passwordFields[0].value = loginData.password;
                                                passwordFields[0].dispatchEvent(new Event('input', { bubbles: true }));
                                                console.log("已填充密码到:", passwordFields[0]);
                                            }
                                        }
                                        
                                        return true;
                                    }
                                    
                                    // 尝试立即填充
                                    let filled = fillLoginForm();
                                    
                                    // 如果没有成功填充，设置多个定时器尝试填充
                                    if (!filled) {
                                        for (let i = 1; i <= 10; i++) {
                                            setTimeout(fillLoginForm, i * 500);
                                        }
                                    }
                                    
                                    // 使用 MutationObserver 监听DOM变化，自动填充延迟加载的表单
                                    if (window.MutationObserver) {
                                        console.log("启动MutationObserver监听表单变化");
                                        const observer = new MutationObserver(function() {
                                            fillLoginForm();
                                        });
                                        
                                        observer.observe(document.body, {
                                            childList: true,
                                            subtree: true
                                        });
                                        
                                        // 7秒后断开观察
                                        setTimeout(function() {
                                            observer.disconnect();
                                            console.log("MutationObserver已断开");
                                        }, 7000);
                                    }
                                })();
                                `;
                                
                                // 创建注入脚本
                                const scriptElem = popup.document.createElement('script');
                                scriptElem.textContent = fillFormScript;
                                
                                // 尝试注入脚本
                                try {
                                    popup.document.head.appendChild(scriptElem);
                                    console.log("已将自动填充脚本注入到弹出窗口");
                                } catch (err) {
                                    console.error("无法注入脚本:", err);
                                    
                                    // 备用方法：如果直接注入失败，则保存数据至localStorage
                                    localStorage.setItem('autofill_data', dataStr);
                                }
                            }
                        } catch (e) {
                            console.error("弹出窗口访问错误:", e);
                            // 备用方法：保存到localStorage
                            localStorage.setItem('autofill_data', dataStr);
                        }
                    }, 300);
                    
                    showMessage(`已打开登录页面并尝试自动填充数据`);
                    return;
                }
                
                showMessage(`正在登录到 ${url}`);
                
                // 实际跳转到系统URL
                setTimeout(() => {
                    window.location.href = url;
                }, 1000);
            });
            
            // 仅打开网站按钮
            openWebsiteBtn.addEventListener('click', function() {
                const url = getSystemUrl(currentSystem);
                if (!url) {
                    showMessage('系统URL配置错误');
                    return;
                }
                
                showMessage(`正在打开 ${url}`);
                
                // 在新窗口打开系统URL
                window.open(url, '_blank');
                
                // 关闭登录面板
                loginPanel.style.display = 'none';
                overlay.style.display = 'none';
            });
            
            // 取消按钮
            cancelBtn.addEventListener('click', function() {
                loginPanel.style.display = 'none';
                overlay.style.display = 'none';
            });
            
            // 点击遮罩也可以关闭登录面板
            overlay.addEventListener('click', function() {
                loginPanel.style.display = 'none';
                overlay.style.display = 'none';
            });
            
            // 显示消息
            function showMessage(text) {
                message.textContent = text;
                message.style.display = 'block';
                
                setTimeout(() => {
                    message.style.display = 'none';
                }, 3000);
            }
            
            // 初始化渲染
            renderSystemTiles();
            
            // 用户信息表单处理
            showUserInfoBtn.addEventListener('click', function() {
                // 尝试加载上次保存的用户信息
                tryLoadSavedUserInfo();
                
                userInfoForm.style.display = 'block';
                showUserInfoBtn.style.display = 'none';
            });
            
            // 尝试加载上次保存的用户信息
            function tryLoadSavedUserInfo() {
                const savedInfo = localStorage.getItem('last_user_info');
                if (savedInfo) {
                    try {
                        const userInfo = JSON.parse(savedInfo);
                        
                        // 更新显示的用户信息
                        updateUserInfoDisplay(userInfo);
                        
                        // 填充编辑表单字段
                        updateUserInfoEditForm(userInfo);
                        
                        // 如果有当前角色名称，更新选择
                        if (userInfo.role && document.querySelector(`[data-role="${userInfo.role}"]`)) {
                            document.querySelectorAll('.role-item').forEach(item => {
                                item.classList.remove('selected');
                            });
                            document.querySelector(`[data-role="${userInfo.role}"]`).classList.add('selected');
                            currentRole = userInfo.role;
                        }
                        
                        // 如果保存了学校编号和用户名，也进行更新
                        if (userInfo.schoolCode) document.getElementById('schoolCode').value = userInfo.schoolCode;
                        if (userInfo.username) document.getElementById('username').value = userInfo.username;
                        
                        console.log('已加载保存的用户信息');
                    } catch (err) {
                        console.error('加载用户信息时出错:', err);
                    }
                }
            }
            
            // 更新用户信息显示
            function updateUserInfoDisplay(userInfo) {
                if (!userInfo) {
                    // 如果没有用户信息，清空所有字段
                    displayNumber.textContent = '';
                    displayAddress.textContent = '';
                    displayDepartment.textContent = '';
                    displayPosition.textContent = '';
                    return;
                }
                
                // 只显示用户录入的数据，没有录入则为空
                displayNumber.textContent = userInfo.number || '';
                displayAddress.textContent = userInfo.address || '';
                displayDepartment.textContent = userInfo.department || '';
                displayPosition.textContent = userInfo.position || '';
                
                // 有值时添加类，无值时移除类
                userInfo.number ? displayNumber.classList.add('has-value') : displayNumber.classList.remove('has-value');
                userInfo.address ? displayAddress.classList.add('has-value') : displayAddress.classList.remove('has-value');
                userInfo.department ? displayDepartment.classList.add('has-value') : displayDepartment.classList.remove('has-value');
                userInfo.position ? displayPosition.classList.add('has-value') : displayPosition.classList.remove('has-value');
            }
            
            // 更新用户信息编辑表单
            function updateUserInfoEditForm(userInfo) {
                editNumber.value = userInfo.number || '';
                editAddress.value = userInfo.address || '';
                editDepartment.value = userInfo.department || '';
                editPosition.value = userInfo.position || '';
            }
            
            // 初始化加载用户信息
            function initializeUserInfo() {
                const savedInfo = localStorage.getItem('last_user_info');
                
                if (savedInfo) {
                    try {
                        const userInfo = JSON.parse(savedInfo);
                        
                        if (userInfo && (userInfo.number || userInfo.address || userInfo.department || userInfo.position)) {
                            // 如果有用户数据，则更新显示并显示面板
                            updateUserInfoDisplay(userInfo);
                            updateUserInfoEditForm(userInfo);
                            userInfoDisplay.style.display = 'block';
                            addUserInfoBtn.style.display = 'none';
                        } else {
                            // 没有实际数据，隐藏面板
                            userInfoDisplay.style.display = 'none';
                            addUserInfoBtn.style.display = 'block';
                        }
                    } catch (err) {
                        console.error('初始化用户信息时出错:', err);
                        userInfoDisplay.style.display = 'none';
                        addUserInfoBtn.style.display = 'block';
                    }
                } else {
                    // 没有保存的信息，隐藏面板
                    userInfoDisplay.style.display = 'none';
                    addUserInfoBtn.style.display = 'block';
                }
            }
            
            // 初始化用户信息
            initializeUserInfo();
            
            // 编辑按钮点击处理
            editUserInfoBtn.addEventListener('click', function() {
                infoEditForm.style.display = 'block';
                this.style.display = 'none';
            });
            
            // 取消编辑按钮
            cancelEditBtn.addEventListener('click', function() {
                infoEditForm.style.display = 'none';
                editUserInfoBtn.style.display = 'inline-block';
            });
            
            // 保存编辑的用户信息
            saveInfoBtn.addEventListener('click', function() {
                // 获取表单数据
                const userInfo = {
                    number: editNumber.value,
                    address: editAddress.value,
                    department: editDepartment.value,
                    position: editPosition.value,
                    timestamp: new Date().toISOString()
                };
                
                // 添加当前选中的角色和系统信息
                if (currentRole) {
                    userInfo.role = currentRole;
                }
                
                if (currentSystem) {
                    userInfo.system = config.systems[currentSystem].name;
                }
                
                // 添加学校编号、用户名信息
                userInfo.schoolCode = document.getElementById('schoolCode').value;
                userInfo.username = document.getElementById('username').value;
                
                // 验证必填字段
                if (!userInfo.number || !userInfo.address) {
                    showMessage('请填写号码和地址');
                    return;
                }
                
                // 更新显示
                updateUserInfoDisplay(userInfo);
                
                // 保存到 localStorage
                localStorage.setItem('last_user_info', JSON.stringify(userInfo));
                
                // 确保显示面板
                userInfoDisplay.style.display = 'block';
                
                // 关闭编辑表单
                infoEditForm.style.display = 'none';
                editUserInfoBtn.style.display = 'inline-block';
                
                // 显示保存成功消息
                showMessage('用户信息已保存');
            });
            
            // 检查是否有自动填充数据
            function checkForAutofillData() {
                const autofillData = localStorage.getItem('autofill_data');
                if (autofillData) {
                    try {
                        const parsedData = JSON.parse(autofillData);
                        console.log("检测到需要自动填充的数据:", parsedData);
                        
                        // 检测页面上是否有登录表单
                        const inputs = document.querySelectorAll('input[type="text"], input[type="password"], input[type="tel"], input[type="email"], input:not([type])');
                        
                        if (inputs.length > 0) {
                            console.log("找到表单输入框，尝试自动填充");
                            
                            // 尝试通过各种方式识别字段
                            // 1. 按输入框类型和位置顺序填充
                            const textInputs = Array.from(inputs).filter(input => input.type !== 'password');
                            const passwordInputs = Array.from(inputs).filter(input => input.type === 'password');
                            
                            // 填充学校编号（第一个输入框）
                            if (parsedData.schoolCode && textInputs.length > 0) {
                                // 尝试找到包含特定文本的占位符
                                const schoolInput = textInputs.find(input => 
                                    input.placeholder && (
                                        input.placeholder.includes('学校') || 
                                        input.placeholder.includes('编号') || 
                                        input.placeholder.includes('编码') ||
                                        input.placeholder.includes('代码')
                                    ) ||
                                    input.name && (
                                        input.name.includes('school') || 
                                        input.name.includes('code')
                                    )
                                );
                                
                                if (schoolInput) {
                                    schoolInput.value = parsedData.schoolCode;
                                    console.log("已通过特征匹配填充学校编号");
                                } else {
                                    // 如果没有找到特定标识，使用第一个文本输入框
                                    textInputs[0].value = parsedData.schoolCode;
                                    console.log("已填充学校编号到第一个文本框");
                                }
                            }
                            
                            // 填充用户名（第二个文本输入框）
                            if (parsedData.username && textInputs.length > 1) {
                                // 尝试找到包含特定文本的占位符
                                const userInput = textInputs.find(input => 
                                    input.placeholder && (
                                        input.placeholder.includes('用户') || 
                                        input.placeholder.includes('账号') ||
                                        input.placeholder.includes('账户') ||
                                        input.placeholder.includes('名称')
                                    ) ||
                                    input.name && (
                                        input.name.includes('user') || 
                                        input.name.includes('name') ||
                                        input.name.includes('account')
                                    )
                                );
                                
                                if (userInput) {
                                    userInput.value = parsedData.username;
                                    console.log("已通过特征匹配填充用户名");
                                } else if (textInputs.length > 1) {
                                    // 如果没有找到特定标识，使用第二个文本输入框
                                    textInputs[1].value = parsedData.username;
                                    console.log("已填充用户名到第二个文本框");
                                } else if (textInputs.length === 1 && !parsedData.schoolCode) {
                                    // 如果只有一个文本框且没有填充学校编号，则填入用户名
                                    textInputs[0].value = parsedData.username;
                                    console.log("仅有一个文本框，已填充用户名");
                                }
                            }
                            
                            // 填充密码
                            if (parsedData.password && passwordInputs.length > 0) {
                                passwordInputs[0].value = parsedData.password;
                                console.log("已填充密码");
                            }
                            
                            // 触发输入事件，以便表单验证可以正确响应
                            inputs.forEach(input => {
                                if (input.value) {
                                    const event = new Event('input', { bubbles: true });
                                    input.dispatchEvent(event);
                                }
                            });
                            
                            // 清除自动填充数据以防止在其他页面上不必要的填充
                            localStorage.removeItem('autofill_data');
                            
                            // 显示反馈消息
                            showMessage('已自动填充选中的登录数据');
                        } else {
                            console.log("未找到表单输入框，稍后将重试");
                        }
                    } catch (error) {
                        console.error('自动填充数据处理出错:', error);
                    }
                }
            }
            
            // 在页面加载完成后检查自动填充
            setTimeout(checkForAutofillData, 1000);
            
            // 设置多次尝试自动填充，以应对可能延迟加载的表单
            if (localStorage.getItem('autofill_data')) {
                // 多次尝试自动填充，以应对可能的延迟加载的表单
                for (let i = 0; i < 5; i++) {
                    setTimeout(checkForAutofillData, 1000 * (i + 1));
                }
                
                // 创建并添加一个自动填充脚本，自动运行以处理动态加载的表单
                const autofillScript = document.createElement('script');
                autofillScript.textContent = `
                    // 自动填充脚本
                    (function() {
                        function attemptAutofill() {
                            const autofillData = localStorage.getItem('autofill_data');
                            if (!autofillData) return;
                            
                            try {
                                const parsedData = JSON.parse(autofillData);
                                const allInputs = document.querySelectorAll('input[type="text"], input[type="password"]');
                                
                                // 按顺序填充表单字段
                                let textInputCount = 0;
                                let pwdInputCount = 0;
                                
                                for (let i = 0; i < allInputs.length; i++) {
                                    const input = allInputs[i];
                                    
                                    if (input.type === 'text' || input.type === 'tel' || input.type === 'email') {
                                        if (textInputCount === 0 && parsedData.schoolCode) {
                                            input.value = parsedData.schoolCode;
                                        } else if (textInputCount === 1 && parsedData.username) {
                                            input.value = parsedData.username;
                                        }
                                        textInputCount++;
                                    } else if (input.type === 'password' && parsedData.password) {
                                        input.value = parsedData.password;
                                        pwdInputCount++;
                                    }
                                }
                                
                                // 在成功填充后移除数据
                                if (textInputCount > 0 || pwdInputCount > 0) {
                                    localStorage.removeItem('autofill_data');
                                    console.log('自动填充完成');
                                }
                            } catch (e) {
                                console.error('自动填充错误:', e);
                            }
                        }
                        
                        // 立即尝试一次
                        attemptAutofill();
                        
                        // 监听DOM变化，处理动态加载的表单
                        if (MutationObserver) {
                            const observer = new MutationObserver(function(mutations) {
                                attemptAutofill();
                            });
                            
                            observer.observe(document.body, {
                                childList: true,
                                subtree: true
                            });
                            
                            // 5秒后停止观察，避免无限监听
                            setTimeout(function() {
                                observer.disconnect();
                            }, 5000);
                        }
                        
                        // 多次尝试，以确保填充成功
                        for (let i = 0; i < 5; i++) {
                            setTimeout(attemptAutofill, 1000 * i);
                        }
                    })();
                `;
                
                document.head.appendChild(autofillScript);
            }
            
            // 导出用户信息为JSON文件
            exportUserInfoBtn.addEventListener('click', function() {
                // 获取当前用户信息
                const savedInfo = localStorage.getItem('last_user_info');
                
                if (savedInfo) {
                    try {
                        const userInfo = JSON.parse(savedInfo);
                        
                        // 转换为格式化的JSON字符串
                        const jsonData = JSON.stringify(userInfo, null, 2);
                        
                        // 创建用于下载的blob和链接
                        const blob = new Blob([jsonData], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        
                        // 创建一个下载链接并触发下载
                        const downloadLink = document.createElement('a');
                        downloadLink.href = url;
                        downloadLink.download = `user_info_${userInfo.username || 'user'}_${new Date().getTime()}.json`;
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                        
                        // 显示导出成功消息
                        showMessage('用户信息已导出为JSON文件');
                    } catch (err) {
                        console.error('导出用户信息时出错:', err);
                        showMessage('导出失败，请重试');
                    }
                } else {
                    showMessage('没有可导出的用户信息');
                }
            });
            
            // 添加用户信息按钮点击处理
            addUserInfoBtn.addEventListener('click', function() {
                // 显示用户信息面板和编辑表单
                userInfoDisplay.style.display = 'block';
                infoEditForm.style.display = 'block';
                editUserInfoBtn.style.display = 'none';
                
                // 隐藏添加按钮
                this.style.display = 'none';
                
                // 清空编辑表单
                editNumber.value = '';
                editAddress.value = '';
                editDepartment.value = '';
                editPosition.value = '';
            });
        });
    </script>
</body>
</html>