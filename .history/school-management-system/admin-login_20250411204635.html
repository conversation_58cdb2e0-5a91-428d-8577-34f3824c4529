<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 添水县第四实验小学"数字食堂"监管平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background-image: url('/Users/<USER>/my_project/school-management-system/images/school-bg.jpg');
            background-size: cover;
            background-position: center top;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #0055a7; /* 作为背景图片的备用 */
            position: relative;
        }
        
        /* 添加叠加层使内容更易读 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 85, 167, 0.4); /* 蓝色半透明叠加 */
            z-index: -1;
        }
        
        .system-title {
            text-align: center;
            color: white;
            font-size: 28px;
            font-weight: bold;
            margin-top: 50px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }
        
        .login-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #4a90e2;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .login-btn:hover {
            background-color: #3a7bd5;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #4a90e2;
            text-decoration: none;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .error-message {
            background-color: #ffebee;
            color: #d32f2f;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="system-title">添水县第四实验小学"数字食堂"监管平台</div>
    
    <div class="login-container">
        <div class="login-title">管理员登录</div>
        
        <div class="error-message" id="errorMessage"></div>
        
        <div class="form-group">
            <label for="adminUsername">管理员账号</label>
            <input type="text" id="adminUsername" placeholder="请输入管理员账号">
        </div>
        
        <div class="form-group">
            <label for="adminPassword">管理员密码</label>
            <input type="password" id="adminPassword" placeholder="请输入管理员密码">
        </div>
        
        <button class="login-btn" id="loginBtn">登录</button>
        
        <div class="back-link">
            <a href="fixed-version-with-config.html">返回主页</a>
        </div>
    </div>
    
    <script src="admin-password.js"></script>
    <script>
        // 动态加载学校背景图片
        fetch('images/school-bg.js')
            .then(response => response.text())
            .then(jsContent => {
                // 执行JS获取图片数据
                eval(jsContent);
                if (typeof schoolBgImage !== 'undefined') {
                    // 设置背景图片
                    document.body.style.backgroundImage = `url(${schoolBgImage})`;
                }
            })
            .catch(error => {
                console.error('加载背景图片失败:', error);
            });
    
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const adminUsername = document.getElementById('adminUsername');
            const adminPassword = document.getElementById('adminPassword');
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            
            // 显示错误消息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
            
            // 登录按钮点击事件
            loginBtn.addEventListener('click', function() {
                const username = adminUsername.value.trim();
                const password = adminPassword.value.trim();
                
                if (!username || !password) {
                    showError('请输入管理员账号和密码');
                    return;
                }
                
                // 验证账号和密码
                if (adminPasswordManager.validate(username, password)) {
                    // 登录成功，设置会话标记
                    sessionStorage.setItem('adminLoggedIn', 'true');
                    
                    // 检查是否为默认密码，如果是，跳转到修改密码页面
                    if (adminPasswordManager.isDefaultPassword()) {
                        const urlParams = new URLSearchParams(window.location.search);
                        const redirect = urlParams.get('redirect') || 'admin-config.html';
                        window.location.href = 'change-password.html?redirect=' + encodeURIComponent(redirect);
                    } else {
                        // 重定向到管理员页面
                        const urlParams = new URLSearchParams(window.location.search);
                        const redirect = urlParams.get('redirect') || 'admin-config.html';
                        window.location.href = redirect;
                    }
                } else {
                    showError('管理员账号或密码不正确');
                }
            });
            
            // 监听回车键
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    loginBtn.click();
                }
            });
        });
    </script>
</body>
</html>