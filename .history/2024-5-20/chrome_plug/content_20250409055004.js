// 电话号码正则表达式
const phoneRegex = /(\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}|\d{11})/g;

// 存储找到的电话号码
let foundNumbers = [];

// 存储用户设置
let userSettings = {
  autoRecognitionEnabled: true,
  showTooltips: true,
  showNamesInline: true
};

// CSS 样式
const phoneStyles = `
  /* 基本图标样式 */
  .fas, .far {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-family: Arial, sans-serif;
    font-weight: 900;
  }
  
  /* 图标代码 */
  .fa-star:before { content: "⭐"; }
  .fa-check-circle:before { content: "✓"; }
  .fa-exclamation-circle:before { content: "❗"; }
  .fa-exclamation-triangle:before { content: "⚠️"; }
  .fa-info-circle:before { content: "ℹ️"; }

  .phone-number {
    position: relative;
    cursor: pointer;
    border-bottom: 1px dotted #4a89dc;
    transition: background-color 0.2s;
  }
  .phone-number:hover {
    background-color: rgba(74, 137, 220, 0.1);
  }
  .phone-tooltip {
    position: absolute;
    bottom: 100%;
    left: 0;
    padding: 8px 12px;
    background-color: #4a89dc;
    color: white;
    border-radius: 4px;
    font-size: 13px;
    font-weight: bold;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10000;
    pointer-events: none;
  }
  .phone-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 15px;
    border-width: 5px;
    border-style: solid;
    border-color: #4a89dc transparent transparent transparent;
  }
  .name-label {
    color: #37bc9b;
    font-weight: bold;
    margin-left: 5px;
    font-size: 0.9em;
  }
  .contact-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #4a89dc;
    border-radius: 50%;
    text-align: center;
    line-height: 16px;
    color: white;
    font-size: 10px;
    margin-left: 3px;
    font-family: Arial, sans-serif;
  }
  .page-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background-color: #4a89dc;
    color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 10001;
    max-width: 300px;
    font-family: 'Segoe UI', Arial, sans-serif;
    animation: slideIn 0.3s, fadeOut 0.3s 3s forwards;
    display: flex;
    align-items: center;
  }
  .page-notification.success { background-color: #37bc9b; }
  .page-notification.error { background-color: #e9573f; }
  .page-notification.warning { background-color: #f6bb42; }
  
  .page-notification i {
    margin-right: 10px;
    font-size: 18px;
  }
  
  .add-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border-radius: 8px;
    padding: 20px;
    z-index: 10001;
    width: 300px;
    font-family: 'Segoe UI', Arial, sans-serif;
  }
  .add-popup h3 {
    margin-top: 0;
    color: #4a89dc;
    border-bottom: 1px solid #e6e9ed;
    padding-bottom: 10px;
  }
  .add-popup input {
    width: 100%;
    padding: 8px;
    margin: 8px 0;
    border: 1px solid #e6e9ed;
    border-radius: 4px;
    box-sizing: border-box;
  }
  .add-popup input:focus {
    outline: none;
    border-color: #4a89dc;
    box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
  }
  .add-popup-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    gap: 10px;
  }
  .add-popup-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
  }
  .add-popup-save {
    background-color: #4a89dc;
    color: white;
  }
  .add-popup-cancel {
    background-color: #e6e9ed;
    color: #434a54;
  }
  
  @keyframes slideIn {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
`;

// 注入CSS
function injectStyles() {
  const style = document.createElement('style');
  style.textContent = phoneStyles;
  document.head.appendChild(style);
}

// 加载设置
function loadSettings(callback) {
  chrome.storage.sync.get('settings', function(data) {
    if (data.settings) {
      userSettings = data.settings;
    }
    
    if (typeof callback === 'function') {
      callback();
    }
  });
}

// 主函数：扫描页面并处理电话号码
function scanForPhoneNumbers() {
  // 如果设置为不启用自动识别，则退出
  if (!userSettings.autoRecognitionEnabled) {
    return;
  }
  
  // 获取所有文本节点
  const textNodes = getTextNodes(document.body);
  
  // 扫描每个文本节点
  textNodes.forEach(node => {
    // 避免重复处理
    if (node.parentNode && node.parentNode.classList && node.parentNode.classList.contains('processed-node')) {
      return;
    }
    
    const text = node.nodeValue;
    if (!text || text.trim() === '') return;
    
    const matches = text.match(phoneRegex);
    
    if (matches) {
      // 记录找到的号码
      matches.forEach(match => {
        const cleanNumber = match.replace(/[-\s]/g, '');
        if (foundNumbers.indexOf(cleanNumber) === -1) {
          foundNumbers.push(cleanNumber);
        }
      });
      
      // 替换文本中的电话号码
      let newText = text;
      matches.forEach(match => {
        const cleanNumber = match.replace(/[-\s]/g, '');
        newText = newText.replace(match, `<span class="phone-number" data-number="${cleanNumber}">${match}</span>`);
      });
      
      // 创建新元素并替换原文本节点
      const span = document.createElement('span');
      span.classList.add('processed-node');
      span.innerHTML = newText;
      
      if (node.parentNode) {
        node.parentNode.replaceChild(span, node);
      }
    }
  });
  
  // 只有当用户设置允许时才添加交互效果
  if (userSettings.showTooltips) {
    addHoverEffects();
  }
  
  // 只有当用户设置允许时才显示名字标签
  if (userSettings.showNamesInline) {
    loadContactsAndAddLabels();
  }
}

// 获取所有文本节点
function getTextNodes(node) {
  const textNodes = [];
  
  function getNodes(node) {
    if (node.nodeType === 3) {  // Text node
      textNodes.push(node);
    } else if (node.nodeType === 1 && node.nodeName !== 'SCRIPT' && node.nodeName !== 'STYLE') {  // Element node
      // 避免处理已处理过的节点
      if (node.classList && node.classList.contains('processed-node')) {
        return;
      }
      
      for (let i = 0; i < node.childNodes.length; i++) {
        getNodes(node.childNodes[i]);
      }
    }
  }
  
  getNodes(node);
  return textNodes;
}

// 添加悬停效果
function addHoverEffects() {
  const phoneElements = document.querySelectorAll('.phone-number:not(.hover-added)');
  
  phoneElements.forEach(element => {
    element.classList.add('hover-added');
    
    element.addEventListener('mouseenter', function() {
      // 避免重复添加
      if (this.querySelector('.phone-tooltip')) return;
      
      const phoneNumber = this.getAttribute('data-number');
      const tooltip = document.createElement('div');
      tooltip.className = 'phone-tooltip';
      
      // 获取联系人数据
      chrome.storage.sync.get(['phoneContacts', 'starredContacts', 'customFields'], function(data) {
        const contacts = data.phoneContacts || {};
        const starred = data.starredContacts || [];
        const customFields = data.customFields || [];
        
        if (contacts[phoneNumber]) {
          const contactData = contacts[phoneNumber];
          const contactName = typeof contactData === 'string' ? contactData : contactData.name;
          const isStarred = starred.includes(phoneNumber);
          
          let tooltipContent = isStarred ? 
            `<span style="color:gold;margin-right:5px;">⭐</span>${contactName}` : 
            contactName;
          
          // 添加自定义字段信息
          if (typeof contactData !== 'string' && contactData.customData && customFields.length > 0) {
            tooltipContent += '<div style="margin-top:5px;border-top:1px solid rgba(255,255,255,0.3);padding-top:5px;">';
            
            customFields.forEach(field => {
              const fieldValue = contactData.customData[field];
              if (fieldValue) {
                tooltipContent += `<div><strong>${field}:</strong> ${fieldValue}</div>`;
              }
            });
            
            tooltipContent += '</div>';
          }
          
          tooltip.innerHTML = tooltipContent;
        } else {
          tooltip.textContent = '未知联系人';
        }
        
        element.appendChild(tooltip);
        
        // 确保tooltip完全可见
        const rect = tooltip.getBoundingClientRect();
        if (rect.left < 0) {
          tooltip.style.left = '0';
          tooltip.style.right = 'auto';
        }
        if (rect.right > window.innerWidth) {
          tooltip.style.left = 'auto';
          tooltip.style.right = '0';
        }
      });
    });
    
    element.addEventListener('mouseleave', function() {
      const tooltip = this.querySelector('.phone-tooltip');
      if (tooltip) {
        this.removeChild(tooltip);
      }
    });
    
    // 点击复制功能
    element.addEventListener('click', function(e) {
      const phoneNumber = this.getAttribute('data-number');
      
      // 创建临时输入框
      const tempInput = document.createElement('input');
      tempInput.value = phoneNumber;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);
      
      // 显示复制成功提示
      const copyNotice = document.createElement('div');
      copyNotice.className = 'phone-tooltip';
      copyNotice.textContent = '已复制到剪贴板';
      copyNotice.style.backgroundColor = '#37bc9b';
      this.appendChild(copyNotice);
      
      setTimeout(() => {
        if (copyNotice.parentNode === this) {
          this.removeChild(copyNotice);
        }
      }, 1500);
      
      e.preventDefault();
      e.stopPropagation();
    });
  });
}

// 加载联系人数据并添加名字标签
function loadContactsAndAddLabels() {
  chrome.storage.sync.get(['phoneContacts', 'starredContacts', 'customFields'], function(data) {
    const contacts = data.phoneContacts || {};
    const starred = data.starredContacts || [];
    const customFields = data.customFields || [];
    
    // 添加标签到找到的电话号码
    const phoneElements = document.querySelectorAll('.phone-number:not(.label-added)');
    phoneElements.forEach(element => {
      const phoneNumber = element.getAttribute('data-number');
      
      if (contacts[phoneNumber]) {
        element.classList.add('label-added');
        
        // 移除已有标签
        const existingLabel = element.querySelector('.name-label');
        if (existingLabel) {
          element.removeChild(existingLabel);
        }
        
        const contactData = contacts[phoneNumber];
        const contactName = typeof contactData === 'string' ? contactData : contactData.name;
        
        const nameLabel = document.createElement('span');
        nameLabel.className = 'name-label';
        
        // 星标联系人显示星标图标
        if (starred.includes(phoneNumber)) {
          nameLabel.innerHTML = ` (${contactName} <span style="color:gold;">⭐</span>)`;
        } else {
          nameLabel.textContent = ` (${contactName})`;
        }
        
        element.appendChild(nameLabel);
      }
    });
  });
}

// 显示页面通知
function showPageNotification(message, type = 'info') {
  // 移除现有通知
  const existingNotifications = document.querySelectorAll('.page-notification');
  existingNotifications.forEach(notif => {
    document.body.removeChild(notif);
  });
  
  // 创建新通知
  const notification = document.createElement('div');
  notification.className = `page-notification ${type}`;
  
  // 添加图标
  let icon = '';
  switch(type) {
    case 'success': icon = '<span style="margin-right:8px;">✓</span>'; break;
    case 'error': icon = '<span style="margin-right:8px;">❗</span>'; break;
    case 'warning': icon = '<span style="margin-right:8px;">⚠️</span>'; break;
    default: icon = '<span style="margin-right:8px;">ℹ️</span>';
  }
  
  notification.innerHTML = `${icon} ${message}`;
  document.body.appendChild(notification);
  
  // 自动移除通知
  setTimeout(() => {
    if (notification.parentNode === document.body) {
      document.body.removeChild(notification);
    }
  }, 3300);
}

// 显示添加联系人弹窗
function showAddPopup(phoneNumber) {
  // 移除现有弹窗
  const existingPopups = document.querySelectorAll('.add-popup');
  existingPopups.forEach(popup => {
    document.body.removeChild(popup);
  });
  
  // 创建弹窗
  chrome.storage.sync.get('customFields', function(data) {
    const customFields = data.customFields || [];
    
    const popup = document.createElement('div');
    popup.className = 'add-popup';
    
    let formContent = `
      <h3>添加联系人</h3>
      <div>
        <input type="text" id="add-popup-number" placeholder="电话号码" value="${phoneNumber || ''}" ${phoneNumber ? 'readonly' : ''}>
        <input type="text" id="add-popup-name" placeholder="姓名" autofocus>
    `;
    
    // 添加自定义字段输入框
    customFields.forEach(field => {
      formContent += `
        <input type="text" id="add-popup-${field}" placeholder="${field}" data-field="${field}">
      `;
    });
    
    formContent += `
      </div>
      <div class="add-popup-buttons">
        <button class="add-popup-button add-popup-cancel">取消</button>
        <button class="add-popup-button add-popup-save">保存</button>
      </div>
    `;
    
    popup.innerHTML = formContent;
    document.body.appendChild(popup);
    
    // 设置按钮事件
    const cancelButton = popup.querySelector('.add-popup-cancel');
    const saveButton = popup.querySelector('.add-popup-save');
    const nameInput = popup.querySelector('#add-popup-name');
    
    // 自动聚焦到姓名输入框
    setTimeout(() => nameInput.focus(), 100);
    
    // 取消按钮事件
    cancelButton.addEventListener('click', () => {
      document.body.removeChild(popup);
    });
    
    // 保存按钮事件
    saveButton.addEventListener('click', () => {
      const number = popup.querySelector('#add-popup-number').value.trim();
      const name = popup.querySelector('#add-popup-name').value.trim();
      
      if (!number || !name) {
        showPageNotification('电话号码和姓名不能为空', 'error');
        return;
      }
      
      // 获取自定义字段值
      const customData = {};
      customFields.forEach(field => {
        const input = popup.querySelector(`#add-popup-${field}`);
        if (input) {
          customData[field] = input.value.trim();
        }
      });
      
      // 将联系人添加到存储
      chrome.storage.sync.get('phoneContacts', function(data) {
        const contacts = data.phoneContacts || {};
        const isNew = !contacts[number];
        
        // 保存为对象格式
        contacts[number] = {
          name: name,
          customData: customData
        };
        
        chrome.storage.sync.set({ 'phoneContacts': contacts }, function() {
          document.body.removeChild(popup);
          showPageNotification(
            isNew ? '联系人添加成功！' : '联系人更新成功！', 
            'success'
          );
          
          // 刷新页面上的联系人标签
          document.querySelectorAll('.phone-number.label-added').forEach(el => {
            el.classList.remove('label-added');
          });
          loadContactsAndAddLabels();
        });
      });
    });
    
    // 支持按Enter键保存
    nameInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        saveButton.click();
      }
    });
  });
}

// 获取当前选中的文本
function getSelectedText() {
  return window.getSelection().toString();
}

// 复制电话号码到剪贴板
function copyToClipboard(text) {
  const tempInput = document.createElement('input');
  tempInput.value = text;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand('copy');
  document.body.removeChild(tempInput);
  
  showPageNotification('已复制到剪贴板: ' + text, 'success');
}

// 处理来自background.js的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  switch(request.action) {
    case "getSelectedText":
      sendResponse({text: getSelectedText()});
      break;
      
    case "showNotification":
      showPageNotification(request.message, request.type);
      sendResponse({success: true});
      break;
      
    case "openPopupForAdd":
      showAddPopup(request.phoneNumber);
      sendResponse({success: true});
      break;
      
    case "copyPhoneNumber":
      copyToClipboard(request.phoneNumber);
      sendResponse({success: true});
      break;
  }
  
  return true; // 保持消息通道开放，支持异步响应
});

// 监听存储变化，更新页面上的电话号码标签
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (namespace === 'sync') {
    if (changes.phoneContacts || changes.starredContacts) {
      // 清除所有标签的label-added类，以便重新添加标签
      document.querySelectorAll('.phone-number.label-added').forEach(el => {
        el.classList.remove('label-added');
      });
      loadContactsAndAddLabels();
    }
    
    if (changes.settings) {
      // 更新设置并重新扫描
      userSettings = changes.settings.newValue;
      
      // 如果设置更改禁用了某些功能，可能需要清理UI
      if (!userSettings.showNamesInline) {
        document.querySelectorAll('.name-label').forEach(label => label.remove());
      }
      
      if (userSettings.autoRecognitionEnabled) {
        scanForPhoneNumbers();
      }
    }
  }
});

// 初始化：注入样式，加载设置，并开始扫描
function initialize() {
  injectStyles();
  loadSettings(function() {
    if (userSettings.autoRecognitionEnabled) {
      scanForPhoneNumbers();
      
      // 定期重新扫描页面（应对动态加载的内容）
      setInterval(scanForPhoneNumbers, 3000);
    }
  });
}

// 页面加载完成后运行
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
} 