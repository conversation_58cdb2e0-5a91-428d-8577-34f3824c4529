// 电话号码正则表达式
const phoneRegex = /(\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}|\d{11})/g;

// 存储找到的电话号码
let foundNumbers = [];

// 全局变量
let settings = {
  autoRecognition: true,
  showTooltips: true,
  showNames: false,
  autoFill: true,
  autoJump: true
};

// CSS 样式
const phoneStyles = `
  /* 基本图标样式 */
  .fas, .far {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-family: Arial, sans-serif;
    font-weight: 900;
  }
  
  /* 图标代码 */
  .fa-star:before { content: "⭐"; }
  .fa-check-circle:before { content: "✓"; }
  .fa-exclamation-circle:before { content: "❗"; }
  .fa-exclamation-triangle:before { content: "⚠️"; }
  .fa-info-circle:before { content: "ℹ️"; }

  .phone-number {
    position: relative;
    cursor: pointer;
    border-bottom: 1px dotted #4a89dc;
    transition: background-color 0.2s;
  }
  .phone-number:hover {
    background-color: rgba(74, 137, 220, 0.1);
  }
  .phone-tooltip {
    position: absolute;
    bottom: 100%;
    left: 0;
    padding: 8px 12px;
    background-color: #4a89dc;
    color: white;
    border-radius: 4px;
    font-size: 13px;
    font-weight: bold;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10000;
    pointer-events: none;
  }
  .phone-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 15px;
    border-width: 5px;
    border-style: solid;
    border-color: #4a89dc transparent transparent transparent;
  }
  .name-label {
    color: #37bc9b;
    font-weight: bold;
    margin-left: 5px;
    font-size: 0.9em;
  }
  .contact-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #4a89dc;
    border-radius: 50%;
    text-align: center;
    line-height: 16px;
    color: white;
    font-size: 10px;
    margin-left: 3px;
    font-family: Arial, sans-serif;
  }
  .page-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background-color: #4a89dc;
    color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 10001;
    max-width: 300px;
    font-family: 'Segoe UI', Arial, sans-serif;
    animation: slideIn 0.3s, fadeOut 0.3s 3s forwards;
    display: flex;
    align-items: center;
  }
  .page-notification.success { background-color: #37bc9b; }
  .page-notification.error { background-color: #e9573f; }
  .page-notification.warning { background-color: #f6bb42; }
  
  .page-notification i {
    margin-right: 10px;
    font-size: 18px;
  }
  
  .add-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    border-radius: 8px;
    padding: 20px;
    z-index: 10001;
    width: 300px;
    font-family: 'Segoe UI', Arial, sans-serif;
  }
  .add-popup h3 {
    margin-top: 0;
    color: #4a89dc;
    border-bottom: 1px solid #e6e9ed;
    padding-bottom: 10px;
  }
  .add-popup input {
    width: 100%;
    padding: 8px;
    margin: 8px 0;
    border: 1px solid #e6e9ed;
    border-radius: 4px;
    box-sizing: border-box;
  }
  .add-popup input:focus {
    outline: none;
    border-color: #4a89dc;
    box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
  }
  .add-popup-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    gap: 10px;
  }
  .add-popup-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
  }
  .add-popup-save {
    background-color: #4a89dc;
    color: white;
  }
  .add-popup-cancel {
    background-color: #e6e9ed;
    color: #434a54;
  }
  
  @keyframes slideIn {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
  
  .autofill-suggestion {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: white;
    border: 1px solid #e6e9ed;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10001;
    width: 100%;
    box-sizing: border-box;
    font-family: 'Segoe UI', Arial, sans-serif;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .autofill-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f5f7fa;
    transition: background-color 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .autofill-item:last-child {
    border-bottom: none;
  }
  
  .autofill-item:hover {
    background-color: #f5f7fa;
  }
  
  .autofill-item .contact-info {
    display: flex;
    flex-direction: column;
  }
  
  .autofill-item .contact-name {
    font-weight: bold;
    color: #434a54;
  }
  
  .autofill-item .contact-number {
    font-size: 0.85em;
    color: #656d78;
  }
  
  .autofill-item .star-icon {
    color: gold;
    margin-left: 8px;
  }
  
  .autofill-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85em;
  }
  
  /* 表单检测提示 */
  .form-detector-prompt {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    padding: 15px;
    z-index: 10001;
    max-width: 300px;
    font-family: 'Segoe UI', Arial, sans-serif;
    animation: slideUp 0.3s;
  }
  
  @keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  .form-detector-prompt h3 {
    margin-top: 0;
    color: #4a89dc;
    margin-bottom: 10px;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  
  .form-detector-prompt h3 i {
    margin-right: 8px;
  }
  
  .form-detector-prompt p {
    margin: 8px 0;
    font-size: 14px;
    color: #434a54;
  }
  
  .form-detector-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
  }
  
  .form-detector-button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
  }
  
  .form-detector-fill {
    background-color: #4a89dc;
    color: white;
  }
  
  .form-detector-cancel {
    background-color: #e6e9ed;
    color: #434a54;
  }
`;

// 注入CSS
function injectStyles() {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .phone-number {
      color: var(--primary-color, #3498db);
      cursor: pointer;
      text-decoration: underline;
      position: relative;
    }
    
    .phone-number:hover {
      color: var(--primary-dark, #2980b9);
    }
    
    .phone-tooltip {
      position: absolute;
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      padding: 8px;
      z-index: 10000;
      width: 200px;
      display: none;
      font-size: 14px;
      color: #333;
    }
    
    .phone-tooltip.active {
      display: block;
    }
    
    .tooltip-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    
    .tooltip-title {
      font-weight: bold;
    }
    
    .tooltip-close {
      cursor: pointer;
      color: #aaa;
      font-size: 12px;
    }
    
    .tooltip-close:hover {
      color: #555;
    }
    
    .tooltip-body {
      margin-bottom: 5px;
    }
    
    .tooltip-contact-name {
      font-weight: bold;
      color: var(--success-color, #27ae60);
      margin-bottom: 5px;
    }
    
    .tooltip-buttons {
      display: flex;
      justify-content: space-between;
    }
    
    .tooltip-button {
      border: none;
      border-radius: 3px;
      padding: 3px 8px;
      cursor: pointer;
      background-color: var(--primary-color, #3498db);
      color: white;
      font-size: 12px;
    }
    
    .tooltip-button:hover {
      background-color: var(--primary-dark, #2980b9);
    }
    
    .tooltip-button.secondary {
      background-color: var(--secondary-color, #95a5a6);
    }
    
    .tooltip-button.secondary:hover {
      background-color: var(--secondary-dark, #7f8c8d);
    }
    
    .name-label {
      display: inline-block;
      margin-left: 5px;
      color: var(--success-color, #27ae60);
      font-style: italic;
      font-size: 0.9em;
    }
    
    .notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      padding: 10px 15px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      z-index: 10001;
      font-size: 14px;
      transition: opacity 0.3s;
      max-width: 300px;
    }
    
    .notification.success {
      border-left: 4px solid var(--success-color, #27ae60);
    }
    
    .notification.error {
      border-left: 4px solid var(--error-color, #e74c3c);
    }
    
    .notification.info {
      border-left: 4px solid var(--primary-color, #3498db);
    }
  `;
  document.head.appendChild(styleElement);
}

// 获取用户设置
function loadSettings() {
  const savedSettings = JSON.parse(localStorage.getItem('settings')) || {};
  settings = {
    autoRecognition: savedSettings.autoRecognition !== false,
    showTooltips: savedSettings.showTooltips !== false,
    showNames: savedSettings.showNames !== false,
    autoFill: savedSettings.autoFill !== false,
    autoJump: savedSettings.autoJump !== false
  };
}

// 主函数：扫描页面并处理电话号码
function scanForPhoneNumbers() {
  // 如果设置为不启用自动识别，则退出
  if (!settings.autoRecognition) {
    return;
  }
  
  // 获取所有文本节点
  const textNodes = getTextNodes(document.body);
  
  // 扫描每个文本节点
  textNodes.forEach(node => {
    // 避免重复处理
    if (node.parentNode && node.parentNode.classList && node.parentNode.classList.contains('processed-node')) {
      return;
    }
    
    const text = node.nodeValue;
    if (!text || text.trim() === '') return;
    
    const matches = text.match(phoneRegex);
    
    if (matches) {
      // 记录找到的号码
      matches.forEach(match => {
        const cleanNumber = match.replace(/[-\s]/g, '');
        if (foundNumbers.indexOf(cleanNumber) === -1) {
          foundNumbers.push(cleanNumber);
        }
      });
      
      // 替换文本中的电话号码
      let newText = text;
      matches.forEach(match => {
        const cleanNumber = match.replace(/[-\s]/g, '');
        newText = newText.replace(match, `<span class="phone-number" data-number="${cleanNumber}">${match}</span>`);
      });
      
      // 创建新元素并替换原文本节点
      const span = document.createElement('span');
      span.classList.add('processed-node');
      span.innerHTML = newText;
      
      if (node.parentNode) {
        node.parentNode.replaceChild(span, node);
      }
    }
  });
  
  // 只有当用户设置允许时才添加交互效果
  if (settings.showTooltips) {
    addHoverEffects();
  }
  
  // 只有当用户设置允许时才显示名字标签
  if (settings.showNames) {
    loadContactsAndAddLabels();
  }
  
  // 检测登录表单并提供自动填充
  if (settings.autoFill) {
    detectLoginForms();
  }
}

// 获取所有文本节点
function getTextNodes(node) {
  const textNodes = [];
  
  function getNodes(node) {
    if (node.nodeType === 3) {  // Text node
      textNodes.push(node);
    } else if (node.nodeType === 1 && node.nodeName !== 'SCRIPT' && node.nodeName !== 'STYLE') {  // Element node
      // 避免处理已处理过的节点
      if (node.classList && node.classList.contains('processed-node')) {
        return;
      }
      
      for (let i = 0; i < node.childNodes.length; i++) {
        getNodes(node.childNodes[i]);
      }
    }
  }
  
  getNodes(node);
  return textNodes;
}

// 添加悬停效果
function addHoverEffects() {
  document.querySelectorAll('.phone-number').forEach(element => {
    const phoneNumber = element.getAttribute('data-phone');
    
    // 查找该号码对应的联系人
    element.addEventListener('click', (e) => {
      e.preventDefault();
      
      const contactsData = localStorage.getItem('phoneContacts');
      const contacts = contactsData ? JSON.parse(contactsData) : {};
      const phoneData = {
        phoneNumber: phoneNumber,
        contactName: '',
        customData: {}
      };
      
      // 检查是否有匹配的联系人
      if (contacts[phoneNumber]) {
        if (typeof contacts[phoneNumber] === 'string') {
          phoneData.contactName = contacts[phoneNumber];
        } else {
          phoneData.contactName = contacts[phoneNumber].name;
          phoneData.customData = contacts[phoneNumber].customData || {};
        }
      }
      
      showPhoneTooltip(element, phoneData);
    });
  });
}

// 加载联系人数据并添加名字标签
function loadContactsAndAddLabels() {
  chrome.storage.sync.get(['phoneContacts', 'starredContacts', 'customFields'], function(data) {
    const contacts = data.phoneContacts || {};
    const starred = data.starredContacts || [];
    const customFields = data.customFields || [];
    
    // 添加标签到找到的电话号码
    const phoneElements = document.querySelectorAll('.phone-number:not(.label-added)');
    phoneElements.forEach(element => {
      const phoneNumber = element.getAttribute('data-number');
      
      if (contacts[phoneNumber]) {
        element.classList.add('label-added');
        
        // 移除已有标签
        const existingLabel = element.querySelector('.name-label');
        if (existingLabel) {
          element.removeChild(existingLabel);
        }
        
        const contactData = contacts[phoneNumber];
        const contactName = typeof contactData === 'string' ? contactData : contactData.name;
        
        const nameLabel = document.createElement('span');
        nameLabel.className = 'name-label';
        
        // 星标联系人显示星标图标
        if (starred.includes(phoneNumber)) {
          nameLabel.innerHTML = ` (${contactName} <span style="color:gold;">⭐</span>)`;
        } else {
          nameLabel.textContent = ` (${contactName})`;
        }
        
        element.appendChild(nameLabel);
      }
    });
  });
}

// 显示页面通知
function showPageNotification(message, type = 'info') {
  // 移除现有通知
  const existingNotifications = document.querySelectorAll('.page-notification');
  existingNotifications.forEach(notif => {
    document.body.removeChild(notif);
  });
  
  // 创建新通知
  const notification = document.createElement('div');
  notification.className = `page-notification ${type}`;
  
  // 添加图标
  let icon = '';
  switch(type) {
    case 'success': icon = '<span style="margin-right:8px;">✓</span>'; break;
    case 'error': icon = '<span style="margin-right:8px;">❗</span>'; break;
    case 'warning': icon = '<span style="margin-right:8px;">⚠️</span>'; break;
    default: icon = '<span style="margin-right:8px;">ℹ️</span>';
  }
  
  notification.innerHTML = `${icon} ${message}`;
  document.body.appendChild(notification);
  
  // 自动移除通知
  setTimeout(() => {
    if (notification.parentNode === document.body) {
      document.body.removeChild(notification);
    }
  }, 3300);
}

// 显示添加联系人弹窗
function showAddPopup(phoneNumber) {
  // 移除现有弹窗
  const existingPopups = document.querySelectorAll('.add-popup');
  existingPopups.forEach(popup => {
    document.body.removeChild(popup);
  });
  
  // 创建弹窗
  chrome.storage.sync.get('customFields', function(data) {
    const customFields = data.customFields || [];
    
    const popup = document.createElement('div');
    popup.className = 'add-popup';
    
    let formContent = `
      <h3>添加联系人</h3>
      <div>
        <input type="text" id="add-popup-number" placeholder="电话号码" value="${phoneNumber || ''}" ${phoneNumber ? 'readonly' : ''}>
        <input type="text" id="add-popup-name" placeholder="姓名" autofocus>
    `;
    
    // 添加自定义字段输入框
    customFields.forEach(field => {
      formContent += `
        <input type="text" id="add-popup-${field}" placeholder="${field}" data-field="${field}">
      `;
    });
    
    formContent += `
      </div>
      <div class="add-popup-buttons">
        <button class="add-popup-button add-popup-cancel">取消</button>
        <button class="add-popup-button add-popup-save">保存</button>
      </div>
    `;
    
    popup.innerHTML = formContent;
    document.body.appendChild(popup);
    
    // 设置按钮事件
    const cancelButton = popup.querySelector('.add-popup-cancel');
    const saveButton = popup.querySelector('.add-popup-save');
    const nameInput = popup.querySelector('#add-popup-name');
    
    // 自动聚焦到姓名输入框
    setTimeout(() => nameInput.focus(), 100);
    
    // 取消按钮事件
    cancelButton.addEventListener('click', () => {
      document.body.removeChild(popup);
    });
    
    // 保存按钮事件
    saveButton.addEventListener('click', () => {
      const number = popup.querySelector('#add-popup-number').value.trim();
      const name = popup.querySelector('#add-popup-name').value.trim();
      
      if (!number || !name) {
        showPageNotification('电话号码和姓名不能为空', 'error');
        return;
      }
      
      // 获取自定义字段值
      const customData = {};
      customFields.forEach(field => {
        const input = popup.querySelector(`#add-popup-${field}`);
        if (input) {
          customData[field] = input.value.trim();
        }
      });
      
      // 将联系人添加到存储
      chrome.storage.sync.get('phoneContacts', function(data) {
        const contacts = data.phoneContacts || {};
        const isNew = !contacts[number];
        
        // 保存为对象格式
        contacts[number] = {
          name: name,
          customData: customData
        };
        
        chrome.storage.sync.set({ 'phoneContacts': contacts }, function() {
          document.body.removeChild(popup);
          showPageNotification(
            isNew ? '联系人添加成功！' : '联系人更新成功！', 
            'success'
          );
          
          // 刷新页面上的联系人标签
          document.querySelectorAll('.phone-number.label-added').forEach(el => {
            el.classList.remove('label-added');
          });
          loadContactsAndAddLabels();
        });
      });
    });
    
    // 支持按Enter键保存
    nameInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        saveButton.click();
      }
    });
  });
}

// 获取当前选中的文本
function getSelectedText() {
  return window.getSelection().toString();
}

// 复制电话号码到剪贴板
function copyToClipboard(text) {
  const tempInput = document.createElement('input');
  tempInput.value = text;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand('copy');
  document.body.removeChild(tempInput);
  
  showPageNotification('已复制到剪贴板: ' + text, 'success');
}

// 处理来自background.js的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  console.log('收到消息:', request);
  
  if (request.action === "getSelectedText") {
    sendResponse({text: getSelectedText()});
  } else if (request.action === "showNotification") {
    showPageNotification(request.message, request.type);
    sendResponse({success: true});
  } else if (request.action === "openPopupForAdd") {
    showAddPopup(request.phoneNumber);
    sendResponse({success: true});
  } else if (request.action === "copyPhoneNumber") {
    copyToClipboard(request.phoneNumber);
    sendResponse({success: true});
  } else if (request.action === "fillContact") {
    fillContactInfo(request.contact);
  } else if (request.action === "settingsUpdated") {
    loadSettings();
    // 更新设置后重新扫描页面
    scanPage();
  }
  
  return true; // 保持消息通道开放，支持异步响应
});

// 监听存储变化，更新页面上的电话号码标签
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (namespace === 'sync') {
    if (changes.phoneContacts || changes.starredContacts) {
      // 清除所有标签的label-added类，以便重新添加标签
      document.querySelectorAll('.phone-number.label-added').forEach(el => {
        el.classList.remove('label-added');
      });
      loadContactsAndAddLabels();
    }
    
    if (changes.settings) {
      // 更新设置并重新扫描
      loadSettings();
      
      // 如果设置更改禁用了某些功能，可能需要清理UI
      if (!settings.showNames) {
        document.querySelectorAll('.name-label').forEach(label => label.remove());
      }
      
      if (settings.autoRecognition) {
        scanForPhoneNumbers();
      }
    }
  }
});

// 添加新函数：检测登录表单
function detectLoginForms() {
  // 尝试查找可能是登录表单的元素
  const forms = document.querySelectorAll('form');
  const phoneInputs = [];
  
  // 检查所有表单
  forms.forEach(form => {
    // 避免重复处理
    if (form.classList.contains('phone-form-processed')) {
      return;
    }
    
    // 标记为已处理
    form.classList.add('phone-form-processed');
    
    // 查找电话号码输入框
    const inputs = form.querySelectorAll('input');
    inputs.forEach(input => {
      // 检查是否可能是电话号码输入框
      if (
        input.type === 'tel' || 
        input.id && input.id.toLowerCase().includes('phone') ||
        input.name && input.name.toLowerCase().includes('phone') ||
        input.placeholder && input.placeholder.toLowerCase().includes('电话') ||
        input.placeholder && input.placeholder.toLowerCase().includes('手机')
      ) {
        // 记录找到的电话输入框
        phoneInputs.push(input);
        
        // 添加事件监听器
        attachAutoFillHandlers(input, form);
      }
    });
  });
  
  // 查找输入输入框（不在表单内的）
  const allInputs = document.querySelectorAll('input');
  allInputs.forEach(input => {
    // 避免重复处理已处理的表单元素
    if (input.classList.contains('phone-input-processed')) {
      return;
    }
    
    // 检查是否可能是电话号码输入框
    if (
      input.type === 'tel' || 
      input.id && input.id.toLowerCase().includes('phone') ||
      input.name && input.name.toLowerCase().includes('phone') ||
      input.placeholder && input.placeholder.toLowerCase().includes('电话') ||
      input.placeholder && input.placeholder.toLowerCase().includes('手机')
    ) {
      // 标记为已处理
      input.classList.add('phone-input-processed');
      
      // 确定可能的相关表单
      const form = input.closest('form') || input.closest('div');
      
      // 添加事件监听器
      attachAutoFillHandlers(input, form);
    }
  });
}

// 添加自动填充处理程序
function attachAutoFillHandlers(input, container) {
  // 添加焦点事件
  input.addEventListener('focus', function() {
    // 创建自动填充建议面板
    createAutoFillSuggestion(input, container);
  });
  
  // 添加输入事件
  input.addEventListener('input', function() {
    // 如果已经输入了部分电话号码（至少3位），提供匹配的建议
    if (this.value.length >= 3) {
      updateAutoFillSuggestion(this, container);
    } else {
      // 移除现有的建议面板
      const existingSuggestion = document.querySelector('.autofill-suggestion');
      if (existingSuggestion) {
        existingSuggestion.remove();
      }
    }
  });
  
  // 添加失焦事件
  input.addEventListener('blur', function(e) {
    // 延迟移除，以便用户可以点击建议
    setTimeout(() => {
      const existingSuggestion = document.querySelector('.autofill-suggestion');
      if (existingSuggestion) {
        existingSuggestion.remove();
      }
    }, 200);
  });
  
  // 只在输入框为空状态的第一次聚焦时弹出提示
  if (!input.dataset.promptShown) {
    input.dataset.promptShown = 'true';
    
    input.addEventListener('focus', function onFirstFocus() {
      if (this.value === '') {
        // 显示自动填充提示
        showFormDetectorPrompt(input, container);
      }
      
      // 移除这个只执行一次的监听器
      input.removeEventListener('focus', onFirstFocus);
    }, { once: true });
  }
}

// 创建自动填充建议
function createAutoFillSuggestion(input, container) {
  // 如果输入框为空，不显示建议
  if (input.value.trim() === '') {
    return;
  }
  
  // 移除现有的建议面板
  const existingSuggestion = document.querySelector('.autofill-suggestion');
  if (existingSuggestion) {
    existingSuggestion.remove();
  }
  
  // 获取联系人数据
  chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
    const contacts = data.phoneContacts || {};
    const starred = data.starredContacts || [];
    
    // 转换为数组以便排序和过滤
    const contactsArray = Object.entries(contacts).map(([phoneNumber, contactData]) => {
      const name = typeof contactData === 'string' ? contactData : contactData.name;
      const customData = typeof contactData === 'string' ? {} : (contactData.customData || {});
      const isStarred = starred.includes(phoneNumber);
      
      return {
        phoneNumber,
        name,
        customData,
        isStarred
      };
    });
    
    // 过滤联系人，只显示包含当前输入的电话号码的联系人
    const filteredContacts = contactsArray.filter(contact => 
      contact.phoneNumber.includes(input.value)
    );
    
    // 如果没有匹配的联系人，不显示建议
    if (filteredContacts.length === 0) {
      return;
    }
    
    // 优先排序星标联系人
    filteredContacts.sort((a, b) => {
      if (a.isStarred && !b.isStarred) return -1;
      if (!a.isStarred && b.isStarred) return 1;
      return a.name.localeCompare(b.name);
    });
    
    // 创建建议面板
    const suggestion = document.createElement('div');
    suggestion.className = 'autofill-suggestion';
    
    // 获取输入框位置
    const rect = input.getBoundingClientRect();
    suggestion.style.width = rect.width + 'px';
    
    // 添加建议条目
    filteredContacts.forEach(contact => {
      const item = document.createElement('div');
      item.className = 'autofill-item';
      
      const contactInfo = document.createElement('div');
      contactInfo.className = 'contact-info';
      
      const contactName = document.createElement('div');
      contactName.className = 'contact-name';
      contactName.textContent = contact.name;
      
      const contactNumber = document.createElement('div');
      contactNumber.className = 'contact-number';
      contactNumber.textContent = contact.phoneNumber;
      
      contactInfo.appendChild(contactName);
      contactInfo.appendChild(contactNumber);
      item.appendChild(contactInfo);
      
      // 添加星标标识
      if (contact.isStarred) {
        const starIcon = document.createElement('span');
        starIcon.className = 'star-icon';
        starIcon.textContent = '⭐';
        item.appendChild(starIcon);
      }
      
      // 添加自动填充按钮
      const fillButton = document.createElement('button');
      fillButton.className = 'autofill-button';
      fillButton.textContent = '自动填充';
      fillButton.addEventListener('click', function() {
        // 填充电话号码
        input.value = contact.phoneNumber;
        
        // 尝试填充相关字段
        autoFillRelatedFields(container, contact);
        
        // 移除建议面板
        suggestion.remove();
        
        // 触发输入事件
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        
        // 显示通知
        showPageNotification('已自动填充联系人信息', 'success');
      });
      
      item.appendChild(fillButton);
      
      // 点击整个条目也填充
      item.addEventListener('click', function(e) {
        if (e.target === item || e.target === contactInfo || e.target === contactName || e.target === contactNumber) {
          // 填充电话号码
          input.value = contact.phoneNumber;
          
          // 尝试填充相关字段
          autoFillRelatedFields(container, contact);
          
          // 移除建议面板
          suggestion.remove();
          
          // 触发输入事件
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
          
          // 显示通知
          showPageNotification('已自动填充联系人信息', 'success');
        }
      });
      
      suggestion.appendChild(item);
    });
    
    // 将建议添加到页面
    document.body.appendChild(suggestion);
    
    // 定位建议面板
    positionElement(suggestion, input);
  });
}

// 更新自动填充建议
function updateAutoFillSuggestion(input, container) {
  // 移除现有的建议面板
  const existingSuggestion = document.querySelector('.autofill-suggestion');
  if (existingSuggestion) {
    existingSuggestion.remove();
  }
  
  // 创建新的建议面板
  createAutoFillSuggestion(input, container);
}

// 显示表单检测提示
function showFormDetectorPrompt(input, container) {
  // 获取联系人数量
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    const contactCount = Object.keys(contacts).length;
    
    // 如果没有联系人，不显示提示
    if (contactCount === 0) {
      return;
    }
    
    // 移除现有的提示
    const existingPrompt = document.querySelector('.form-detector-prompt');
    if (existingPrompt) {
      existingPrompt.remove();
    }
    
    // 创建提示
    const prompt = document.createElement('div');
    prompt.className = 'form-detector-prompt';
    
    prompt.innerHTML = `
      <h3><i class="fas fa-check-circle"></i> 检测到电话输入框</h3>
      <p>您已保存 ${contactCount} 个联系人，是否要从已保存的联系人中自动填充信息？</p>
      <div class="form-detector-buttons">
        <button class="form-detector-button form-detector-cancel">取消</button>
        <button class="form-detector-button form-detector-fill">显示联系人</button>
      </div>
    `;
    
    // 添加按钮事件
    const cancelButton = prompt.querySelector('.form-detector-cancel');
    const fillButton = prompt.querySelector('.form-detector-fill');
    
    cancelButton.addEventListener('click', function() {
      prompt.remove();
    });
    
    fillButton.addEventListener('click', function() {
      prompt.remove();
      createAutoFillSuggestion(input, container);
    });
    
    // 添加到页面
    document.body.appendChild(prompt);
    
    // 5秒后自动消失
    setTimeout(() => {
      if (prompt.parentNode === document.body) {
        prompt.remove();
      }
    }, 5000);
  });
}

// 自动填充相关字段
function autoFillRelatedFields(container, contact) {
  // 根据字段名称和ID等特征查找相关的输入字段
  const inputs = container.querySelectorAll('input, textarea, select');
  
  // 收集所有可填充的输入字段
  const fillableInputs = [];
  
  inputs.forEach(input => {
    const id = input.id ? input.id.toLowerCase() : '';
    const name = input.name ? input.name.toLowerCase() : '';
    const placeholder = input.placeholder ? input.placeholder.toLowerCase() : '';
    
    // 检查是否是电话号码字段
    const isPhoneField = 
      input.type === 'tel' || 
      id.includes('phone') || 
      name.includes('phone') || 
      placeholder.includes('电话') || 
      placeholder.includes('手机');
    
    // 检查是否是姓名字段
    const isNameField = 
      id.includes('name') || 
      name.includes('name') || 
      placeholder.includes('姓名') ||
      placeholder.includes('名字');
    
    // 检查是否匹配自定义字段
    let matchingCustomField = null;
    if (contact.customData) {
      for (const [fieldName, fieldValue] of Object.entries(contact.customData)) {
        const lowerFieldName = fieldName.toLowerCase();
        if (
          id.includes(lowerFieldName) || 
          name.includes(lowerFieldName) || 
          placeholder.includes(lowerFieldName)
        ) {
          matchingCustomField = fieldName;
          break;
        }
      }
    }
    
    // 如果是可填充的字段，加入数组
    if (isPhoneField || isNameField || matchingCustomField) {
      fillableInputs.push({
        element: input,
        isPhoneField,
        isNameField,
        customField: matchingCustomField,
        tabIndex: input.tabIndex || 0
      });
    }
  });
  
  // 按照tabIndex排序，确保按照正确的顺序填充
  fillableInputs.sort((a, b) => a.tabIndex - b.tabIndex);
  
  // 填充字段
  let lastFilledInput = null;
  
  fillableInputs.forEach(input => {
    if (input.isPhoneField) {
      // 填充电话号码
      input.element.value = contact.phoneNumber;
      input.element.dispatchEvent(new Event('input', { bubbles: true }));
      input.element.dispatchEvent(new Event('change', { bubbles: true }));
      lastFilledInput = input.element;
    } else if (input.isNameField) {
      // 填充姓名
      input.element.value = contact.name;
      input.element.dispatchEvent(new Event('input', { bubbles: true }));
      input.element.dispatchEvent(new Event('change', { bubbles: true }));
      lastFilledInput = input.element;
    } else if (input.customField && contact.customData[input.customField]) {
      // 填充自定义字段
      input.element.value = contact.customData[input.customField];
      input.element.dispatchEvent(new Event('input', { bubbles: true }));
      input.element.dispatchEvent(new Event('change', { bubbles: true }));
      lastFilledInput = input.element;
    }
  });
  
  // 找出下一个可能需要手动填写的空输入框
  let nextEmptyInput = null;
  
  // 获取表单中的所有输入元素（包括不可见的）
  const allInputs = Array.from(container.querySelectorAll('input:not([type="hidden"]), textarea, select'))
    .filter(el => !el.disabled && !el.readOnly);
  
  if (lastFilledInput && allInputs.length > 0) {
    // 找到最后填充的输入框的索引
    const lastIndex = allInputs.indexOf(lastFilledInput);
    
    // 寻找下一个空的输入框
    if (lastIndex !== -1 && lastIndex < allInputs.length - 1) {
      for (let i = lastIndex + 1; i < allInputs.length; i++) {
        const input = allInputs[i];
        // 检查输入框是否为空且可见
        if ((!input.value || input.value.trim() === '') && isElementVisible(input)) {
          nextEmptyInput = input;
          break;
        }
      }
    }
  }
  
  // 如果找到了下一个空输入框，自动聚焦到它
  if (nextEmptyInput) {
    // 使用短暂的延迟确保DOM更新和事件处理已完成
    setTimeout(() => {
      // 聚焦到下一个输入框并可能滚动到视图
      nextEmptyInput.focus();
      
      // 如果元素不在视口中，滚动到它
      if (!isElementInViewport(nextEmptyInput)) {
        nextEmptyInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      
      // 如果是select元素，模拟点击以显示选项
      if (nextEmptyInput.tagName.toLowerCase() === 'select') {
        nextEmptyInput.click();
      }
    }, 100);
  }
}

// 检查元素是否可见
function isElementVisible(element) {
  if (!element) return false;
  
  const style = window.getComputedStyle(element);
  return style.display !== 'none' && 
         style.visibility !== 'hidden' && 
         style.opacity !== '0' &&
         element.offsetWidth > 0 &&
         element.offsetHeight > 0;
}

// 检查元素是否在视口中
function isElementInViewport(element) {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

// 将元素定位在参考元素附近
function positionElement(element, referenceElement) {
  const refRect = referenceElement.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  // 默认定位在输入框下方
  let top = refRect.bottom + scrollTop;
  let left = refRect.left + scrollLeft;
  
  // 确保水平方向不超出屏幕
  const elementWidth = element.offsetWidth || refRect.width;
  if (left + elementWidth > window.innerWidth) {
    left = window.innerWidth - elementWidth - 10;
  }
  
  // 检查是否有足够空间在下方显示
  if (refRect.bottom + element.offsetHeight > window.innerHeight && refRect.top > element.offsetHeight) {
    // 如果下方空间不足且上方空间足够，则显示在上方
    top = refRect.top + scrollTop - element.offsetHeight;
  }
  
  element.style.position = 'absolute';
  element.style.top = top + 'px';
  element.style.left = left + 'px';
}

// 尝试识别并保存未知的联系人
function detectAndSaveUnknownContact(input, container) {
  const phoneNumber = input.value.trim();
  
  // 确保是有效的电话号码
  if (!phoneNumber.match(/\d{11}/) && !phoneNumber.match(/\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}/)) {
    return;
  }
  
  // 检查是否已存在该联系人
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    const cleanNumber = phoneNumber.replace(/[-\s]/g, '');
    
    // 如果联系人已存在，不做任何处理
    if (contacts[cleanNumber]) {
      return;
    }
    
    // 尝试获取姓名
    const nameInput = findNameInput(container);
    if (nameInput && nameInput.value.trim()) {
      // 找到姓名输入框，并且有输入内容
      const name = nameInput.value.trim();
      
      // 获取自定义字段
      const customData = {};
      
      // 提示用户是否保存
      showSaveContactPrompt(cleanNumber, name, customData);
    }
  });
}

// 找到可能的姓名输入框
function findNameInput(container) {
  const inputs = container.querySelectorAll('input');
  
  for (let input of inputs) {
    const id = input.id ? input.id.toLowerCase() : '';
    const name = input.name ? input.name.toLowerCase() : '';
    const placeholder = input.placeholder ? input.placeholder.toLowerCase() : '';
    
    if (
      id.includes('name') || 
      name.includes('name') || 
      placeholder.includes('姓名') ||
      placeholder.includes('名字')
    ) {
      return input;
    }
  }
  
  return null;
}

// 显示保存联系人提示
function showSaveContactPrompt(phoneNumber, name, customData) {
  // 移除现有的提示
  const existingPrompt = document.querySelector('.form-detector-prompt');
  if (existingPrompt) {
    existingPrompt.remove();
  }
  
  // 创建提示
  const prompt = document.createElement('div');
  prompt.className = 'form-detector-prompt';
  
  prompt.innerHTML = `
    <h3><i class="fas fa-user-plus"></i> 发现新联系人</h3>
    <p>是否将 ${name} (${phoneNumber}) 保存到您的联系人列表？</p>
    <div class="form-detector-buttons">
      <button class="form-detector-button form-detector-cancel">取消</button>
      <button class="form-detector-button form-detector-fill">保存</button>
    </div>
  `;
  
  // 添加按钮事件
  const cancelButton = prompt.querySelector('.form-detector-cancel');
  const saveButton = prompt.querySelector('.form-detector-fill');
  
  cancelButton.addEventListener('click', function() {
    prompt.remove();
  });
  
  saveButton.addEventListener('click', function() {
    // 保存联系人
    chrome.storage.sync.get('phoneContacts', function(data) {
      const contacts = data.phoneContacts || {};
      
      contacts[phoneNumber] = {
        name: name,
        customData: customData
      };
      
      chrome.storage.sync.set({ 'phoneContacts': contacts }, function() {
        prompt.remove();
        showPageNotification('联系人已保存', 'success');
      });
    });
  });
  
  // 添加到页面
  document.body.appendChild(prompt);
  
  // 8秒后自动消失
  setTimeout(() => {
    if (prompt.parentNode === document.body) {
      prompt.remove();
    }
  }, 8000);
}

// 监听表单提交
function listenForFormSubmissions() {
  // 如果设置为不启用自动填充，则退出
  if (!settings.autoFill) {
    return;
  }
  
  // 监听所有表单的提交事件
  document.addEventListener('submit', function(e) {
    const form = e.target;
    
    // 查找可能的电话号码输入框
    const phoneInputs = form.querySelectorAll('input[type="tel"], input[id*="phone"], input[name*="phone"]');
    
    phoneInputs.forEach(input => {
      // 尝试检测并保存未知的联系人
      detectAndSaveUnknownContact(input, form);
    });
  }, true);
}

// 填充联系人信息到表单
function fillContactInfo(contact) {
  console.log('填充联系人信息:', contact);
  let filled = false;
  
  // 查找所有输入框
  const inputFields = document.querySelectorAll('input[type="text"], input[type="tel"], input[type="email"], textarea');
  const inputFieldsArray = Array.from(inputFields);
  
  // 尝试填充电话号码
  if (contact.phone) {
    const phoneFields = inputFieldsArray.filter(field => {
      const fieldId = field.id.toLowerCase();
      const fieldName = field.name.toLowerCase();
      const fieldPlaceholder = (field.placeholder || '').toLowerCase();
      return fieldId.includes('phone') || fieldName.includes('phone') || 
             fieldId.includes('tel') || fieldName.includes('tel') || 
             fieldPlaceholder.includes('phone') || fieldPlaceholder.includes('电话') ||
             fieldPlaceholder.includes('手机');
    });
    
    if (phoneFields.length > 0) {
      phoneFields[0].value = contact.phone;
      phoneFields[0].dispatchEvent(new Event('input', { bubbles: true }));
      filled = true;
      
      // 如果自动跳转功能开启，尝试找到下一个输入框并聚焦
      if (settings.autoJump && phoneFields[0] !== inputFieldsArray[inputFieldsArray.length - 1]) {
        const currentIndex = inputFieldsArray.indexOf(phoneFields[0]);
        if (currentIndex !== -1 && currentIndex < inputFieldsArray.length - 1) {
          // 找到下一个输入框
          const nextField = inputFieldsArray[currentIndex + 1];
          nextField.focus();
        }
      }
    }
  }
  
  // 尝试填充姓名
  if (contact.name) {
    const nameFields = inputFieldsArray.filter(field => {
      const fieldId = field.id.toLowerCase();
      const fieldName = field.name.toLowerCase();
      const fieldPlaceholder = (field.placeholder || '').toLowerCase();
      return fieldId.includes('name') || fieldName.includes('name') || 
             fieldPlaceholder.includes('name') || fieldPlaceholder.includes('姓名') ||
             fieldPlaceholder.includes('联系人');
    });
    
    if (nameFields.length > 0) {
      nameFields[0].value = contact.name;
      nameFields[0].dispatchEvent(new Event('input', { bubbles: true }));
      filled = true;
      
      // 如果自动跳转功能开启，尝试找到下一个输入框并聚焦
      if (settings.autoJump && nameFields[0] !== inputFieldsArray[inputFieldsArray.length - 1]) {
        const currentIndex = inputFieldsArray.indexOf(nameFields[0]);
        if (currentIndex !== -1 && currentIndex < inputFieldsArray.length - 1) {
          // 找到下一个输入框
          const nextField = inputFieldsArray[currentIndex + 1];
          nextField.focus();
        }
      }
    }
  }
  
  // ... existing code for other fields ...
  
  return filled;
}

// 初始化：注入样式，加载设置，并开始扫描
function initialize() {
  injectStyles();
  loadSettings();
  
  if (settings.autoRecognition) {
    scanForPhoneNumbers();
    
    // 定期重新扫描页面（应对动态加载的内容）
    setInterval(scanForPhoneNumbers, 3000);
  }
  
  // 监听表单提交
  listenForFormSubmissions();
  
  // 监听设置变更
  window.addEventListener('storage', function(e) {
    if (e.key === 'settings') {
      loadSettings();
    }
  });
}

// 页面加载完成后运行
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// 显示通知消息
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  document.body.appendChild(notification);
  
  // 3秒后自动移除通知
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}

// 显示电话号码工具提示
function showPhoneTooltip(element, phoneData) {
  if (!settings.showTooltips) return;
  
  // 移除现有的工具提示
  const existingTooltips = document.querySelectorAll('.phone-tooltip');
  existingTooltips.forEach(tip => tip.remove());
  
  // 创建新的工具提示
  const tooltip = document.createElement('div');
  tooltip.className = 'phone-tooltip';
  
  // 获取联系人信息
  const hasContact = phoneData.contactName && phoneData.contactName.length > 0;
  
  // 创建工具提示内容
  let tooltipContent = `
    <div class="tooltip-header">
      <span class="tooltip-title">电话号码</span>
      <span class="tooltip-close">&times;</span>
    </div>
    <div class="tooltip-body">
      <div>${phoneData.phoneNumber}</div>
  `;
  
  // 如果有联系人信息，则显示
  if (hasContact) {
    tooltipContent += `<div class="tooltip-contact-name">${phoneData.contactName}</div>`;
    
    // 显示自定义字段
    if (phoneData.customData) {
      for (const [field, value] of Object.entries(phoneData.customData)) {
        if (value && value.trim()) {
          tooltipContent += `<div><strong>${field}:</strong> ${value}</div>`;
        }
      }
    }
  }
  
  tooltipContent += `
    </div>
    <div class="tooltip-buttons">
      <button class="tooltip-button" id="copy-number">复制号码</button>
      ${!hasContact ? '<button class="tooltip-button secondary" id="add-contact">添加联系人</button>' : ''}
    </div>
  `;
  
  tooltip.innerHTML = tooltipContent;
  
  // 将工具提示插入到页面中
  document.body.appendChild(tooltip);
  
  // 计算位置
  const rect = element.getBoundingClientRect();
  tooltip.style.left = `${rect.left}px`;
  tooltip.style.top = `${rect.bottom + 5}px`;
  
  // 确保工具提示不超出页面边界
  const tooltipRect = tooltip.getBoundingClientRect();
  if (tooltipRect.right > window.innerWidth) {
    tooltip.style.left = `${window.innerWidth - tooltipRect.width - 10}px`;
  }
  
  // 显示工具提示
  setTimeout(() => tooltip.classList.add('active'), 10);
  
  // 绑定事件
  tooltip.querySelector('.tooltip-close').addEventListener('click', () => {
    tooltip.remove();
  });
  
  // 复制号码
  tooltip.querySelector('#copy-number').addEventListener('click', () => {
    navigator.clipboard.writeText(phoneData.phoneNumber)
      .then(() => {
        showNotification('号码已复制到剪贴板', 'success');
        tooltip.remove();
      })
      .catch(() => {
        showNotification('复制失败，请手动复制', 'error');
      });
  });
  
  // 添加联系人
  const addContactBtn = tooltip.querySelector('#add-contact');
  if (addContactBtn) {
    addContactBtn.addEventListener('click', () => {
      window.postMessage({
        type: 'PHONE_ACTION',
        action: 'addContact',
        phoneNumber: phoneData.phoneNumber
      }, '*');
      tooltip.remove();
    });
  }
  
  // 点击其他地方关闭工具提示
  document.addEventListener('click', function closeTooltip(e) {
    if (!tooltip.contains(e.target) && e.target !== element) {
      tooltip.remove();
      document.removeEventListener('click', closeTooltip);
    }
  });
}

// 为电话号码添加悬停效果
function addHoverEffects() {
  document.querySelectorAll('.phone-number').forEach(element => {
    const phoneNumber = element.getAttribute('data-phone');
    
    // 查找该号码对应的联系人
    element.addEventListener('click', (e) => {
      e.preventDefault();
      
      const contactsData = localStorage.getItem('phoneContacts');
      const contacts = contactsData ? JSON.parse(contactsData) : {};
      const phoneData = {
        phoneNumber: phoneNumber,
        contactName: '',
        customData: {}
      };
      
      // 检查是否有匹配的联系人
      if (contacts[phoneNumber]) {
        if (typeof contacts[phoneNumber] === 'string') {
          phoneData.contactName = contacts[phoneNumber];
        } else {
          phoneData.contactName = contacts[phoneNumber].name;
          phoneData.customData = contacts[phoneNumber].customData || {};
        }
      }
      
      showPhoneTooltip(element, phoneData);
    });
  });
} 