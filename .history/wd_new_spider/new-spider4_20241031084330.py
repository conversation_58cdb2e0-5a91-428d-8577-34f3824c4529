import requests
from pprint import pprint
import urllib3
import os
from tqdm import tqdm
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

# 禁用 InsecureRequestWarning 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF文件下载器")
        self.root.geometry("600x400")
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建输入字段
        self.create_input_fields()
        
        # 创建下载按钮
        self.download_button = ttk.Button(self.main_frame, text="开始下载", command=self.start_download)
        self.download_button.grid(row=6, column=0, columnspan=2, pady=10)
        
        # 创建进度条
        self.progress = ttk.Progressbar(self.main_frame, orient="horizontal", length=400, mode="determinate")
        self.progress.grid(row=7, column=0, columnspan=2, pady=10)
        
        # 创建状态标签
        self.status_label = ttk.Label(self.main_frame, text="")
        self.status_label.grid(row=8, column=0, columnspan=2)

    def create_input_fields(self):
        # Authorization输入
        ttk.Label(self.main_frame, text="Authorization:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.auth_entry = ttk.Entry(self.main_frame, width=50)
        self.auth_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Cookie输入
        ttk.Label(self.main_frame, text="Cookie:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.cookie_entry = ttk.Entry(self.main_frame, width=50)
        self.cookie_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 基础URL输入
        ttk.Label(self.main_frame, text="基础URL:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.base_url_entry = ttk.Entry(self.main_frame, width=50)
        self.base_url_entry.insert(0, "https://kc.zhixueyun.com")
        self.base_url_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 文件ID输入
        ttk.Label(self.main_frame, text="文件ID:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.file_id_entry = ttk.Entry(self.main_frame, width=50)
        self.file_id_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # _值输入
        ttk.Label(self.main_frame, text="_ 值:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.timestamp_entry = ttk.Entry(self.main_frame, width=50)
        self.timestamp_entry.grid(row=4, column=1, sticky=tk.W, pady=5)
        
        # 保存路径选择
        ttk.Label(self.main_frame, text="保存路径:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.save_path_frame = ttk.Frame(self.main_frame)
        self.save_path_frame.grid(row=5, column=1, sticky=tk.W, pady=5)
        
        self.save_path_entry = ttk.Entry(self.save_path_frame, width=40)
        self.save_path_entry.pack(side=tk.LEFT)
        
        self.browse_button = ttk.Button(self.save_path_frame, text="浏览", command=self.browse_save_path)
        self.browse_button.pack(side=tk.LEFT, padx=5)

    def browse_save_path(self):
        directory = filedialog.askdirectory()
        if directory:
            self.save_path_entry.delete(0, tk.END)
            self.save_path_entry.insert(0, directory)

    def update_progress(self, current, total):
        progress = (current / total) * 100
        self.progress["value"] = progress
        self.root.update_idletasks()

    def start_download(self):
        # 获取输入值
        headers = {
            "Accept": "*/*",
            "Accept-Language": "zh,zh-CN;q=0.9,en;q=0.8",
            "Authorization": self.auth_entry.get(),
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Cookie": self.cookie_entry.get(),
            "Referer": "https://kc.zhixueyun.com/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        }
        
        base_url = self.base_url_entry.get()
        file_id = self.file_id_entry.get()
        timestamp = self.timestamp_entry.get()
        save_path = self.save_path_entry.get()

        # 验证输入
        if not all([headers["Authorization"], headers["Cookie"], base_url, file_id, timestamp]):
            messagebox.showerror("错误", "请填写所有必要的信息")
            return

        # 创建下载器实例
        downloader = PDFDownloader(headers, base_url)
        
        try:
            # 获取文件信息
            self.status_label["text"] = "正在获取文件信息..."
            file_url, filename = downloader.fetch_file_info(file_id)
            
            if file_url and filename:
                output_filename = os.path.join(save_path, filename)
                self.status_label["text"] = f"正在下载: {filename}"
                
                # 修改download_pdf方法以支持进度更新
                downloader.download_pdf(file_url, output_filename, self.update_progress)
                
                self.status_label["text"] = "下载完成！"
                messagebox.showinfo("成功", f"文件已保存至: {output_filename}")
            else:
                self.status_label["text"] = "获取文件信息失败"
                messagebox.showerror("错误", "无法获取文件信息")
        except Exception as e:
            self.status_label["text"] = "下载过程中出现错误"
            messagebox.showerror("错误", str(e))
        finally:
            self.progress["value"] = 0

class PDFDownloader:
    def __init__(self, headers, base_url):
        self.headers = headers
        self.base_url = base_url

    def fetch_file_info(self, file_id):
        """获取文件信息，返回文件下载的路径 URL 和文件名。"""
        url = f"{self.base_url}/api/v1/tools-center-v2/file-cloud/preview"
        params = {
            "id": file_id,
            "_": self.timestamp_entry.get()
        }
        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False)
            response.raise_for_status()
            data = response.json()
            if not isinstance(data, dict):
                logger.error("响应格式无效，未能获取文件信息。")
                return None, None
            file_url = data.get("url")
            filename = data.get("filename")
            if not file_url or not filename:
                logger.error("文件信息不完整，请检查文件 ID 是否正确。")
                return None, None
            return file_url, filename
        except requests.exceptions.RequestException as e:
            logger.error(f"请求文件信息失败: {e}")
            return None, None

    def download_pdf(self, file_url, output_filename, progress_callback=None):
        """下载 PDF 文件并保存到指定路径。"""
        full_url = f"{self.base_url}{file_url}"
        try:
            response = requests.get(full_url, headers=self.headers, verify=False, stream=True)
            response.raise_for_status()
            total_size = int(response.headers.get('content-length', 0))
            current_size = 0
            
            with open(output_filename, "wb") as f:
                for data in response.iter_content(chunk_size=1024):
                    f.write(data)
                    current_size += len(data)
                    if progress_callback:
                        progress_callback(current_size, total_size)
                        
            logger.info(f"文件已保存为 {output_filename}")
        except requests.exceptions.RequestException as e:
            logger.error(f"PDF 文件下载失败: {e}")
            raise

def main():
    root = tk.Tk()
    app = PDFDownloaderGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
