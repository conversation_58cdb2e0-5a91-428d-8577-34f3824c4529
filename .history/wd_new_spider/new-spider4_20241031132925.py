import os
# 在文件最开始，其他导入之前添加
os.environ['TK_SILENCE_DEPRECATION'] = '1'  # 禁止 Tkinter 的弃用警告
os.environ['PYTHONWARNINGS'] = 'ignore'  # 忽略 Python 警告

import requests
from pprint import pprint
import urllib3
import os
from tqdm import tqdm
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from queue import Queue
from threading import Thread
from ttkthemes import ThemedTk, THEMES
from languages import LANGUAGES
from datetime import datetime
from PIL import Image
import pystray
from plyer import notification
from packaging import version
import time
from user_manager import UserManager  # 添加导入

# 禁用 InsecureRequestWarning 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFDownloaderGUI:
    def __init__(self, root, username):
        self.root = root
        self.root.title("电信网上大学PDF文件下载器")
        self.root.geometry("900x600")
        
        # 保存当前用户名
        self.current_username = username
        
        # 添加配置文件的默认路径
        self.config_file = "pdf_downloader_config.json"
        
        # 初始化主题样式
        self.style = ttk.Style()
        # 获取所有可用主题
        self.available_themes = self.root.get_themes()  # 用 ThemedTk 的方法获取主题
        self.current_theme = tk.StringVar(value=self.root.current_theme)  # 获取当前主题
        
        # 添加语言设置
        self.current_language = tk.StringVar(value="中文")
        self.available_languages = list(LANGUAGES.keys())
        
        # 创建所有框架
        self._create_frames()
        
        # 创建所有界面组件
        self.create_menu_bar()
        self.create_left_panel()
        self.create_right_panel()
        self.create_bottom_panel()
        
        # 更新界面文本
        self.update_ui_text()
        
        # 尝试加载已保存的配置
        self.load_config()
        
        # 初始化系统托盘
        self.setup_system_tray()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 添加更新检查器
        self.update_checker = UpdateChecker()
        
        # 检查更新
        self.check_for_updates()
        
        # 初始化用户管理器
        self.user_manager = UserManager()

    def _create_frames(self):
        """创建所有主要框架"""
        # 创建菜单栏框架
        self.menu_frame = ttk.Frame(self.root)
        self.menu_frame.pack(fill=tk.X, padx=10, pady=(5,0))
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        self.left_frame = ttk.LabelFrame(self.main_frame, text="参数设置", padding="10")
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))
        
        self.right_frame = ttk.LabelFrame(self.main_frame, text="处理日志", padding="10")
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 创建底部框架
        self.bottom_frame = ttk.Frame(self.root, padding="10")
        self.bottom_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # 创建日志文本框
        self.log_text = tk.Text(self.right_frame, height=20, width=50)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)

    def browse_save_path(self):
        """打开文件夹选择对话框"""
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.save_path_entry.delete(0, tk.END)
            self.save_path_entry.insert(0, folder_path)

    def validate_save_path(self, path):
        """验证保存路径"""
        if not path:
            self.log_message("错误: 请选择保存路径")
            return False
        if not os.path.exists(path):
            try:
                os.makedirs(path)
            except Exception as e:
                self.log_message(f"错误: 创建保存路径失败 - {str(e)}")
                return False
        return True

    def create_left_panel(self):
        # Authorization输入
        ttk.Label(self.left_frame, text="Authorization:").pack(anchor=tk.W, pady=(0, 2))
        self.auth_entry = ttk.Entry(self.left_frame, width=40)
        self.auth_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Cookie输入
        ttk.Label(self.left_frame, text="Cookie:").pack(anchor=tk.W, pady=(0, 2))
        self.cookie_entry = ttk.Entry(self.left_frame, width=40)
        self.cookie_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 基础URL输入
        ttk.Label(self.left_frame, text="基础URL:").pack(anchor=tk.W, pady=(0, 2))
        self.base_url_entry = ttk.Entry(self.left_frame, width=40)
        self.base_url_entry.insert(0, "https://kc.zhixueyun.com")
        self.base_url_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 文件ID输入
        ttk.Label(self.left_frame, text="文件ID:").pack(anchor=tk.W, pady=(0, 2))
        self.file_id_entry = ttk.Entry(self.left_frame, width=40)
        self.file_id_entry.pack(fill=tk.X, pady=(0, 10))
        
        # _值输入
        ttk.Label(self.left_frame, text="_ 值:").pack(anchor=tk.W, pady=(0, 2))
        self.timestamp_entry = ttk.Entry(self.left_frame, width=40)
        self.timestamp_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 保存路径选择
        ttk.Label(self.left_frame, text="保存路径:").pack(anchor=tk.W, pady=(0, 2))
        save_path_frame = ttk.Frame(self.left_frame)
        save_path_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.save_path_entry = ttk.Entry(save_path_frame)
        self.save_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.browse_button = ttk.Button(save_path_frame, text="浏览", command=self.browse_save_path)
        self.browse_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 创建按钮框架
        button_frame = ttk.Frame(self.left_frame)
        button_frame.pack(pady=(20, 0))
        
        # 添加开始下载按钮
        self.download_button = ttk.Button(button_frame, text="始下载", command=self.start_download)
        self.download_button.pack(side=tk.LEFT, padx=5)
        
        # 添加保存配置按钮
        self.save_config_button = ttk.Button(button_frame, text="保存配置", command=self.save_config)
        self.save_config_button.pack(side=tk.LEFT, padx=5)
        
        # ���加批量下载按钮
        self.batch_download_button = ttk.Button(
            button_frame,
            text=LANGUAGES[self.current_language.get()]["batch_download"],
            command=self.show_batch_download_dialog
        )
        self.batch_download_button.pack(side=tk.LEFT, padx=5)

    def create_right_panel(self):
        # 创建notebook用于切换日志和任务列表
        self.notebook = ttk.Notebook(self.right_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 日志页面
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="日志")
        
        # 任务列表页面
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text="下载任务")
        
        # 创建任务列表
        self.task_tree = ttk.Treeview(self.task_frame, columns=("ID", "状态", "进度"))
        self.task_tree.heading("文件ID", text="文ID")
        self.task_tree.heading("状态", text="状态")
        self.task_tree.heading("进度", text="进度")
        self.task_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加日志操作按钮框架
        log_buttons_frame = ttk.Frame(self.log_frame)
        log_buttons_frame.pack(fill=tk.X, pady=5)
        
        # 添加清除日志按钮
        self.clear_log_button = ttk.Button(
            log_buttons_frame,
            text=LANGUAGES[self.current_language.get()]["clear_log"],
            command=self.clear_log
        )
        self.clear_log_button.pack(side=tk.LEFT, padx=5)
        
        # 添加导出日志按钮
        self.export_log_button = ttk.Button(
            log_buttons_frame,
            text=LANGUAGES[self.current_language.get()]["export_log"],
            command=self.export_log
        )
        self.export_log_button.pack(side=tk.LEFT, padx=5)

    def create_bottom_panel(self):
        # 状态标签
        self.status_label = ttk.Label(self.bottom_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(self.bottom_frame, orient="horizontal", 
                                      length=400, mode="determinate")
        self.progress.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

    def log_message(self, message):
        """向日志文框添加消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def start_download(self):
        # 清空日志
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 获取输入值
        headers = {
            "Accept": "*/*",
            "Accept-Language": "zh,zh-CN;q=0.9,en;q=0.8",
            "Authorization": self.auth_entry.get(),
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Cookie": self.cookie_entry.get(),
            "Referer": "https://kc.zhixueyun.com/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        }
        
        base_url = self.base_url_entry.get()
        file_id = self.file_id_entry.get()
        timestamp = self.timestamp_entry.get()
        save_path = self.save_path_entry.get()

        # 验证输入
        if not all([headers["Authorization"], headers["Cookie"], base_url, file_id, timestamp]):
            self.log_message("错误: 请填写所有必要的息")
            return

        # 验证保存路径
        if not self.validate_save_path(save_path):
            return

        # 创建下载器实例
        downloader = PDFDownloader(headers, base_url)
        
        try:
            # 获取文件信息
            self.status_label["text"] = "正在获取文件信息..."
            self.log_message("正在获取文件信息...")
            file_url, filename = downloader.fetch_file_info(file_id, timestamp)
            
            if file_url and filename:
                output_filename = os.path.join(save_path, filename)
                
                # 检查文件是否已存在
                if os.path.exists(output_filename):
                    if not messagebox.askyesno("文件已存", 
                        f"文件 {filename} 已存在，是否覆盖？"):
                        self.log_message("下载已取消")
                        return
                
                self.status_label["text"] = f"在载: {filename}"
                self.log_message(f"开始下载文件: {filename}")
                
                # 下载文件
                downloader.download_pdf(file_url, output_filename, self.update_progress)
                
                self.status_label["text"] = "下载完成！"
                self.log_message(f"文件已保存至: {output_filename}")
            else:
                self.status_label["text"] = "获取文件信息失败"
                self.log_message("错误: 无法获取文件信息")
        except Exception as e:
            self.status_label["text"] = "下载过程中出现错误"
            self.log_message(f"错误: {str(e)}")
        finally:
            self.progress["value"] = 0

    def update_progress(self, current, total):
        progress = (current / total) * 100
        self.progress["value"] = progress
        self.status_label["text"] = f"下载进度: {progress:.1f}%"
        self.root.update_idletasks()

    def save_config(self):
        """保存当前配置到文件（更新版本）"""
        config = {
            'authorization': self.auth_entry.get(),
            'cookie': self.cookie_entry.get(),
            'base_url': self.base_url_entry.get(),
            'save_path': self.save_path_entry.get(),
            'theme': self.current_theme.get()  # 存主题设置
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.log_message("配置已功保存")
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载配置文件（更新版本）"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 设置已保存的值
                self.auth_entry.delete(0, tk.END)
                self.auth_entry.insert(0, config.get('authorization', ''))
                
                self.cookie_entry.delete(0, tk.END)
                self.cookie_entry.insert(0, config.get('cookie', ''))
                
                self.base_url_entry.delete(0, tk.END)
                self.base_url_entry.insert(0, config.get('base_url', 'https://kc.zhixueyun.com'))
                
                self.save_path_entry.delete(0, tk.END)
                self.save_path_entry.insert(0, config.get('save_path', ''))
                
                # 加载主题设置
                saved_theme = config.get('theme')
                if saved_theme and saved_theme in self.available_themes:
                    self.style.theme_use(saved_theme)
                    self.current_theme.set(saved_theme)
                
                # 加载语言设置
                saved_language = config.get('language')
                if saved_language and saved_language in self.available_languages:
                    self.current_language.set(saved_language)
                    self.update_ui_text()
                
                self.log_message(LANGUAGES[self.current_language.get()]["config_loaded"])
        except Exception as e:
            self.log_message(f"{LANGUAGES[self.current_language.get()]['error_prefix']}{str(e)}")

    def create_menu_bar(self):
        """创建顶部菜单栏"""
        # 创建语言选择区域
        ttk.Label(self.menu_frame, text="Language:").pack(side=tk.LEFT, padx=(0,5))
        
        # 创建语言选择下拉框
        self.language_combobox = ttk.Combobox(
            self.menu_frame,
            textvariable=self.current_language,
            values=self.available_languages,
            state="readonly",
            width=10
        )
        self.language_combobox.pack(side=tk.LEFT, padx=(0, 10))
        
        # 绑定语言切换事件
        self.language_combobox.bind('<<ComboboxSelected>>', self.change_language)
        
        # 创建主题选区域
        ttk.Label(self.menu_frame, text="主题设置:").pack(side=tk.LEFT, padx=(0,5))
        
        # 创建主题选择下拉框
        self.theme_combobox = ttk.Combobox(
            self.menu_frame, 
            textvariable=self.current_theme,
            values=self.available_themes,
            state="readonly",
            width=15
        )
        self.theme_combobox.pack(side=tk.LEFT, padx=(0, 10))
        
        # 添加主题应用按钮
        self.apply_theme_button = ttk.Button(
            self.menu_frame,
            text="应用主题",
            command=lambda: self.change_theme()
        )
        self.apply_theme_button.pack(side=tk.LEFT, padx=5)
        
        # 添加用户授权按钮（只对root用户显示）
        if self.current_username == "root":
            self.auth_button = ttk.Button(
                self.menu_frame,
                text=LANGUAGES[self.current_language.get()]["user_auth"],
                command=self.show_auth_dialog
            )
            self.auth_button.pack(side=tk.RIGHT, padx=5)
        
        # 添加用户管理按钮（只对root用户显示）
        if self.current_username == "root":
            self.user_manage_button = ttk.Button(
                self.menu_frame,
                text=LANGUAGES[self.current_language.get()]["user_manage"],
                command=self.show_user_manage_dialog
            )
            self.user_manage_button.pack(side=tk.RIGHT, padx=5)
        
        # 添加设置按钮
        self.settings_button = ttk.Button(
            self.menu_frame,
            text="设置",
            command=self.open_settings
        )
        self.settings_button.pack(side=tk.RIGHT, padx=5)
        
        # 绑定主题切换事件
        self.theme_combobox.bind('<<ComboboxSelected>>', self.change_theme)

    def change_theme(self, event=None):
        """切换主题"""
        new_theme = self.current_theme.get()
        try:
            # 使用 ThemedTk 的方法切换主题
            self.root.set_theme(new_theme)
            self.save_theme_preference(new_theme)
            self.log_message(f"主题已切换为: {new_theme}")
            
            # 更新所有窗口部件的外观
            self.root.update_idletasks()
            
            # 显示成功消息
            messagebox.showinfo("成功", f"主题已切换为: {new_theme}")
        except Exception as e:
            self.log_message(f"切换主题失败: {str(e)}")
            messagebox.showerror("错误", f"切换主题失败: {str(e)}")

    def save_theme_preference(self, theme):
        """保存主题偏好到配置文件"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config['theme'] = theme
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存主题偏好失败: {e}")

    def open_settings(self):
        """打开设置对话框"""
        settings = SettingsDialog(self.root, self.config_file)

    def change_language(self, event=None):
        """切换语言"""
        new_language = self.current_language.get()
        self.update_ui_text()
        self.save_language_preference(new_language)
        self.log_message(f"Language changed to: {new_language}")

    def update_ui_text(self):
        """更新界面文本"""
        lang = LANGUAGES[self.current_language.get()]
        
        # 更新窗口标题
        self.root.title(lang["title"])
        
        # 更新框架标题
        self.left_frame.configure(text=lang["settings"])
        self.right_frame.configure(text=lang["log"])
        
        # 更新标签文本
        for widget in self.left_frame.winfo_children():
            if isinstance(widget, ttk.Label):
                text = widget.cget("text")
                for key, value in lang.items():
                    if text.endswith(":"):
                        text = text[:-1]
                    if text == value:
                        widget.configure(text=lang[key])
        
        # 更新按钮文本
        self.browse_button.configure(text=lang["browse"])
        self.download_button.configure(text=lang["start_download"])
        self.save_config_button.configure(text=lang["save_config"])
        self.apply_theme_button.configure(text=lang["apply_theme"])
        self.settings_button.configure(text=lang["settings_btn"])

    def save_language_preference(self, language):
        """保存语言偏好到配置文件"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config['language'] = language
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存语言偏好失败: {e}")

    def clear_log(self):
        """清除日志内容"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message(LANGUAGES[self.current_language.get()]["log_cleared"])

    def export_log(self):
        """导出日志到文件"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title=LANGUAGES[self.current_language.get()]["export_log_title"]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo(
                    LANGUAGES[self.current_language.get()]["success"],
                    LANGUAGES[self.current_language.get()]["log_exported"]
                )
            except Exception as e:
                messagebox.showerror(
                    LANGUAGES[self.current_language.get()]["error"],
                    f"{LANGUAGES[self.current_language.get()]['export_log_failed']}: {str(e)}"
            )

    def show_batch_download_dialog(self):
        """显示批量下载对话框"""
        dialog = BatchDownloadDialog(self.root, self)
        self.root.wait_window(dialog.dialog)

    def setup_system_tray(self):
        """设置系统托盘"""
        # 创建系统托盘图标
        image = Image.new('RGB', (64, 64), color = 'red')
        menu = (
            pystray.MenuItem(
                LANGUAGES[self.current_language.get()]["show_window"], 
                self.show_window
            ),
            pystray.MenuItem(
                LANGUAGES[self.current_language.get()]["exit"], 
                self.quit_app
            )
        )
        self.tray_icon = pystray.Icon(
            "pdf_downloader",
            image,
            LANGUAGES[self.current_language.get()]["title"],
            menu
        )
        
    def show_window(self, icon, item):
        """显示主窗口"""
        self.root.deiconify()
        
    def hide_window(self):
        """隐藏主窗"""
        self.root.withdraw()
        
    def quit_app(self, icon, item):
        """退出应用"""
        icon.stop()
        self.root.destroy()
        
    def on_closing(self):
        """窗口关闭的处理"""
        if messagebox.askyesno(
            LANGUAGES[self.current_language.get()]["minimize_to_tray"],
            LANGUAGES[self.current_language.get()]["minimize_to_tray_msg"]
        ):
            self.hide_window()
            # 启动系托盘图标
            Thread(target=self.tray_icon.run, daemon=True).start()
        else:
            self.quit_app(self.tray_icon, None)

    def show_notification(self, title, message):
        """显示系统通知"""
        try:
            notification.notify(
                title=title,
                message=message,
                app_icon=None,  # 可以添加自定义图标
                timeout=10
            )
        except Exception as e:
            logger.error(f"显示通失败: {e}")

    def check_for_updates(self):
        """检查更新"""
        has_update, latest_version = self.update_checker.check_for_updates()
        if has_update:
            if messagebox.askyesno(
                LANGUAGES[self.current_language.get()]["update_available"],
                LANGUAGES[self.current_language.get()]["update_msg"].format(latest_version)
            ):
                self.open_update_page()

    def show_auth_dialog(self):
        """显示用户授权对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(LANGUAGES["中文"]["user_auth"])
        dialog.geometry("500x400")
        
        # 创建用户列表框架
        list_frame = ttk.LabelFrame(dialog, text=LANGUAGES["中文"]["user_list"], padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建用户列表
        columns = ("username", "status", "permissions", "created_at")
        user_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # 设置列标题
        user_tree.heading("username", text=LANGUAGES["中文"]["username"])
        user_tree.heading("status", text=LANGUAGES["中文"]["status"])
        user_tree.heading("permissions", text=LANGUAGES["中文"]["permissions"])
        user_tree.heading("created_at", text=LANGUAGES["中文"]["created_at"])
        
        # 设置列宽
        user_tree.column("username", width=100)
        user_tree.column("status", width=80)
        user_tree.column("permissions", width=150)
        user_tree.column("created_at", width=150)
        
        user_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=user_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        user_tree.configure(yscrollcommand=scrollbar.set)
        
        def load_users():
            """加载用户列表"""
            user_tree.delete(*user_tree.get_children())
            for username, user_data in self.user_manager.users.items():
                if username != "root":  # 不显示root用户
                    status = LANGUAGES["中文"]["activated"] if user_data["is_active"] else LANGUAGES["中文"]["not_activated"]
                    permissions = ", ".join(user_data.get("permissions", []))
                    user_tree.insert("", tk.END, values=(
                        username,
                        status,
                        permissions,
                        user_data["created_at"]
                    ))
        
        def activate_selected_user():
            """激活选中的用户"""
            selected = user_tree.selection()
            if not selected:
                messagebox.showwarning(
                    LANGUAGES["中文"]["warning"],
                    LANGUAGES["中文"]["no_user_selected"]
                )
                return
            
            # 弹出密码验证对话框
            pwd_dialog = tk.Toplevel(dialog)
            pwd_dialog.title("管理员验证")
            pwd_dialog.geometry("300x150")
            
            ttk.Label(pwd_dialog, text="请输入管理员密码:").pack(pady=10)
            pwd_entry = ttk.Entry(pwd_dialog, show="*")
            pwd_entry.pack(pady=5)
            
            def do_activate():
                admin_pwd = pwd_entry.get()
                username = user_tree.item(selected[0])["values"][0]
                
                success, message = self.user_manager.activate_user(
                    "root",
                    admin_pwd,
                    username
                )
                
                if success:
                    messagebox.showinfo(LANGUAGES["中文"]["success"], message)
                    pwd_dialog.destroy()
                    load_users()  # 刷新用户列表
                else:
                    messagebox.showerror(LANGUAGES["中文"]["error"], message)
            
            ttk.Button(pwd_dialog, text="确认", command=do_activate).pack(pady=10)
            ttk.Button(pwd_dialog, text="取消", command=pwd_dialog.destroy).pack(pady=5)
            
            # 设置模态对话框
            pwd_dialog.transient(dialog)
            pwd_dialog.grab_set()
        
        def update_permissions():
            """更新用户权限"""
            selected = user_tree.selection()
            if not selected:
                messagebox.showwarning(
                    LANGUAGES["中文"]["warning"],
                    LANGUAGES["中文"]["no_user_selected"]
                )
                return
            
            # 弹出密码验证对话框
            pwd_dialog = tk.Toplevel(dialog)
            pwd_dialog.title("管理员验证")
            pwd_dialog.geometry("300x150")
            
            ttk.Label(pwd_dialog, text="请输入管理员密码:").pack(pady=10)
            pwd_entry = ttk.Entry(pwd_dialog, show="*")
            pwd_entry.pack(pady=5)
            
            def do_update():
                admin_pwd = pwd_entry.get()
                username = user_tree.item(selected[0])["values"][0]
                new_permissions = [
                    perm for perm, var in permission_vars.items()
                    if var.get()
                ]
                
                success, message = self.user_manager.update_user_permissions(
                    "root",
                    admin_pwd,
                    username,
                    new_permissions
                )
                
                if success:
                    messagebox.showinfo(LANGUAGES["中文"]["success"], message)
                    pwd_dialog.destroy()
                    load_users()  # 刷新用户列表
                else:
                    messagebox.showerror(LANGUAGES["中文"]["error"], message)
            
            ttk.Button(pwd_dialog, text="确认", command=do_update).pack(pady=10)
            ttk.Button(pwd_dialog, text="取消", command=pwd_dialog.destroy).pack(pady=5)
            
            # 设置模态对话框
            pwd_dialog.transient(dialog)
            pwd_dialog.grab_set()
        
        # 权限管理框架
        perm_frame = ttk.LabelFrame(dialog, text=LANGUAGES["中文"]["permission_management"], padding="10")
        perm_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 权限选择
        available_permissions = ["download", "upload", "edit", "delete"]  # 可用的权限列表
        permission_vars = {perm: tk.BooleanVar() for perm in available_permissions}
        
        for perm in available_permissions:
            ttk.Checkbutton(
                perm_frame,
                text=LANGUAGES["中文"][f"perm_{perm}"],
                variable=permission_vars[perm]
            ).pack(side=tk.LEFT, padx=5)
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 添加激活按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["activate_user"],
            command=activate_selected_user
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加权限更新按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["update_permissions"],
            command=update_permissions
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加刷新按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["refresh"],
            command=load_users
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加关闭按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["close"],
            command=dialog.destroy
        ).pack(side=tk.RIGHT, padx=5)
        
        # 初始加载用户列表
        load_users()

    def show_user_manage_dialog(self):
        """显示用户管理对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(LANGUAGES[self.current_language.get()]["user_manage"])
        dialog.geometry("600x400")
        
        # 创建用户列表框架
        list_frame = ttk.LabelFrame(dialog, text=LANGUAGES[self.current_language.get()]["user_list"], padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建用户列表
        columns = ("username", "status", "permissions", "created_at", "last_login")
        user_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # 设置列标题
        user_tree.heading("username", text=LANGUAGES[self.current_language.get()]["username"])
        user_tree.heading("status", text=LANGUAGES[self.current_language.get()]["status"])
        user_tree.heading("permissions", text=LANGUAGES[self.current_language.get()]["permissions"])
        user_tree.heading("created_at", text=LANGUAGES[self.current_language.get()]["created_at"])
        user_tree.heading("last_login", text=LANGUAGES[self.current_language.get()]["last_login"])
        
        # 设置列宽
        user_tree.column("username", width=100)
        user_tree.column("status", width=80)
        user_tree.column("permissions", width=150)
        user_tree.column("created_at", width=130)
        user_tree.column("last_login", width=130)
        
        user_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=user_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        user_tree.configure(yscrollcommand=scrollbar.set)
        
        def load_users():
            """加载用户列表"""
            user_tree.delete(*user_tree.get_children())
            for username, user_data in self.user_manager.users.items():
                status = LANGUAGES[self.current_language.get()]["activated"] if user_data["is_active"] else LANGUAGES[self.current_language.get()]["not_activated"]
                permissions = ", ".join(user_data.get("permissions", []))
                last_login = user_data.get("last_login", "-")
                user_tree.insert("", tk.END, values=(
                    username,
                    status,
                    permissions,
                    user_data["created_at"],
                    last_login
                ))
        
        # 用户详情框架
        detail_frame = ttk.LabelFrame(dialog, text=LANGUAGES[self.current_language.get()]["user_details"], padding="10")
        detail_frame.pack(fill=tk.X, padx=10, pady=5)
        
        def show_user_details(event):
            """显示用户详细信息"""
            selected = user_tree.selection()
            if not selected:
                return
            
            username = user_tree.item(selected[0])["values"][0]
            user_data = self.user_manager.get_user(username)
            
            detail_text = f"""
用户名: {username}
状态: {"已激活" if user_data["is_active"] else "未激活"}
权限: {", ".join(user_data.get("permissions", []))}
创建时间: {user_data["created_at"]}
最后登录: {user_data.get("last_login", "-")}
管理员: {"是" if user_data.get("is_admin", False) else "否"}
            """
            
            detail_label.config(text=detail_text)
        
        # 创建详情标签
        detail_label = ttk.Label(detail_frame, text="", justify=tk.LEFT)
        detail_label.pack(fill=tk.X, padx=5, pady=5)
        
        # 绑定选择事件
        user_tree.bind("<<TreeviewSelect>>", show_user_details)
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 添加刷新按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES[self.current_language.get()]["refresh"],
            command=load_users
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加关闭按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES[self.current_language.get()]["close"],
            command=dialog.destroy
        ).pack(side=tk.RIGHT, padx=5)
        
        # 初始加载用户列表
        load_users()

class DownloadError(Exception):
    """自定义下载异常类"""
    pass

class PDFDownloader:
    def __init__(self, headers, base_url):
        self.headers = headers
        self.base_url = base_url

    def fetch_file_info(self, file_id, timestamp):
        """获取文件信息，返回文件下载的路径 URL 和文件名。"""
        url = f"{self.base_url}/api/v1/tools-center-v2/file-cloud/preview"
        params = {
            "id": file_id,
            "_": timestamp
        }
        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False, timeout=10)
            response.raise_for_status()
            data = response.json()
            if not isinstance(data, dict):
                raise DownloadError("响应格式无效，未能获取文件信息")
            file_url = data.get("url")
            filename = data.get("filename")
            if not file_url or not filename:
                raise DownloadError("文件信息不完整，检查文件 ID 是否正确")
            return file_url, filename
        except requests.Timeout:
            raise DownloadError("请求超时，请检查网络连接")
        except requests.RequestException as e:
            raise DownloadError(f"请求文件信息失败: {str(e)}")

    def download_pdf(self, file_url, output_filename, progress_callback=None):
        """下载 PDF 文件并保存到指定路径。"""
        full_url = f"{self.base_url}{file_url}"
        try:
            response = requests.get(full_url, headers=self.headers, verify=False, stream=True)
            response.raise_for_status()
            total_size = int(response.headers.get('content-length', 0))
            current_size = 0
            
            with open(output_filename, "wb") as f:
                for data in response.iter_content(chunk_size=1024):
                    f.write(data)
                    current_size += len(data)
                    if progress_callback:
                        progress_callback(current_size, total_size)
                        
            logger.info(f"文件已保存为 {output_filename}")
        except requests.exceptions.RequestException as e:
            logger.error(f"PDF 文件下载失败: {e}")
            raise

    def download_with_retry(self, func, *args, max_retries=3, **kwargs):
        """带重试机制的下载函数"""
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"下载失败，正在重试 ({attempt + 1}/{max_retries}): {e}")
                time.sleep(2 ** attempt)  # 指数退避

class ConfigManager:
    def __init__(self, config_file):
        self.config_file = config_file
        self.default_config = {
            'authorization': '',
            'cookie': '',
            'base_url': 'https://kc.zhixueyun.com',
            'save_path': ''
        }

    def load(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return {**self.default_config, **json.load(f)}
            return self.default_config.copy()
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return self.default_config.copy()

    def save(self, config):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False

class DownloadTask:
    def __init__(self, file_id, timestamp, output_path):
        self.file_id = file_id
        self.timestamp = timestamp
        self.output_path = output_path
        self.status = "pending"
        self.progress = 0

class DownloadManager:
    def __init__(self, downloader):
        self.downloader = downloader
        self.task_queue = Queue()
        self.tasks = {}
        self._running = False
        self._worker = None

    def add_task(self, task):
        """添加下载任务"""
        self.task_queue.put(task)
        self.tasks[task.file_id] = task
        if not self._running:
            self._start_worker()

    def _start_worker(self):
        """启动工作线程"""
        self._running = True
        self._worker = Thread(target=self._process_queue, daemon=True)
        self._worker.start()

    def _process_queue(self):
        """处理下载队列"""
        while self._running:
            try:
                task = self.task_queue.get(timeout=1)
                self._process_task(task)
                self.task_queue.task_done()
            except Queue.Empty:
                continue

    def _process_task(self, task):
        """处理单个下载任务"""
        try:
            task.status = "downloading"
            file_url, filename = self.downloader.fetch_file_info(task.file_id, task.timestamp)
            if file_url and filename:
                output_path = os.path.join(task.output_path, filename)
                self.downloader.download_pdf(file_url, output_path, 
                                          lambda c, t: self._update_progress(task, c, t))
                task.status = "completed"
            else:
                task.status = "failed"
        except Exception as e:
            task.status = "failed"
            logger.error(f"下载任务失败: {str(e)}")

    def _update_progress(self, task, current, total):
        """更新下载进度"""
        task.progress = (current / total) * 100

class SettingsDialog:
    def __init__(self, parent, config):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("设置")
        self.dialog.geometry("400x300")
        self.config = config
        
        # 创建设置选项
        self.create_settings_ui()
        
    def create_settings_ui(self):
        # 代理设置
        proxy_frame = ttk.LabelFrame(self.dialog, text="代理设置", padding="5")
        proxy_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(proxy_frame, text="HTTP代理:").pack(anchor=tk.W)
        self.proxy_entry = ttk.Entry(proxy_frame)
        self.proxy_entry.pack(fill=tk.X)
        
        # 下载设置
        download_frame = ttk.LabelFrame(self.dialog, text="下载设置", padding="5")
        download_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(download_frame, text="并发下载数:").pack(anchor=tk.W)
        self.concurrent_spinbox = ttk.Spinbox(download_frame, from_=1, to=5)
        self.concurrent_spinbox.pack(fill=tk.X)

class BatchDownloadDialog:
    def __init__(self, parent, main_app):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(LANGUAGES[main_app.current_language.get()]["batch_download"])
        self.dialog.geometry("500x400")
        self.main_app = main_app
        
        # 创建文本框用于输入文件ID列表
        self.text_frame = ttk.LabelFrame(
            self.dialog,
            text=LANGUAGES[main_app.current_language.get()]["file_id_list"]
        )
        self.text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.id_text = tk.Text(self.text_frame, height=10)
        self.id_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(
            button_frame,
            text=LANGUAGES[main_app.current_language.get()]["start_batch_download"],
            command=self.start_batch_download
        ).pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            button_frame,
            text=LANGUAGES[main_app.current_language.get()]["cancel"],
            command=self.dialog.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def start_batch_download(self):
        """开始批量下载"""
        file_ids = self.id_text.get(1.0, tk.END).strip().split('\n')
        file_ids = [fid.strip() for fid in file_ids if fid.strip()]
        
        if not file_ids:
            messagebox.showwarning(
                LANGUAGES[self.main_app.current_language.get()]["warning"],
                LANGUAGES[self.main_app.current_language.get()]["no_file_ids"]
            )
            return
            
        self.dialog.destroy()
        self.main_app.batch_download_files(file_ids)

class DownloadHistory:
    def __init__(self, history_file="download_history.json"):
        self.history_file = history_file
        self.history = self.load_history()

    def load_history(self):
        """加载下载历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception:
            return []

    def add_record(self, file_id, filename, status, timestamp):
        """添加下载记录"""
        record = {
            "file_id": file_id,
            "filename": filename,
            "status": status,
            "timestamp": timestamp,
            "download_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.history.append(record)
        self.save_history()

    def save_history(self):
        """保存下载史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存下载历史失败: {e}")

class UpdateChecker:
    def __init__(self):
        self.current_version = "1.0.0"
        self.update_url = "https://api.github.com/repos/yourusername/yourrepo/releases/latest"

    def check_for_updates(self):
        """检查更新"""
        try:
            response = requests.get(self.update_url)
            response.raise_for_status()
            latest_version = response.json()["tag_name"]
            
            if version.parse(latest_version) > version.parse(self.current_version):
                return True, latest_version
            return False, None
        except Exception as e:
            logger.error(f"检查更新失败: {e}")
            return False, None

class LoginWindow:
    def __init__(self, root):
        self.root = root
        self.root.title(LANGUAGES["中文"]["login_title"])
        
        # 初始化用户管理器
        self.user_manager = UserManager()
        
        # 设置窗口大小和位置
        window_width = 400
        window_height = 350  # 增加高度以容纳注册按钮
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="20")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 添加logo或标题
        title_label = ttk.Label(
            self.main_frame,
            text=LANGUAGES["中文"]["login_welcome"],
            font=("Helvetica", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 用户名输入框
        username_frame = ttk.Frame(self.main_frame)
        username_frame.pack(fill=tk.X, pady=5)
        ttk.Label(username_frame, text=LANGUAGES["中文"]["username"]).pack(side=tk.LEFT)
        self.username_entry = ttk.Entry(username_frame)
        self.username_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        
        # 密码输入框
        password_frame = ttk.Frame(self.main_frame)
        password_frame.pack(fill=tk.X, pady=5)
        ttk.Label(password_frame, text=LANGUAGES["中文"]["password"]).pack(side=tk.LEFT)
        self.password_entry = ttk.Entry(password_frame, show="*")
        self.password_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        
        # 记住密码选项
        self.remember_var = tk.BooleanVar(value=False)
        remember_check = ttk.Checkbutton(
            self.main_frame,
            text=LANGUAGES["中文"]["remember_password"],
            variable=self.remember_var
        )
        remember_check.pack(pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(pady=20)
        
        # 登录按钮
        self.login_button = ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["login"],
            command=self.login,
            width=15
        )
        self.login_button.pack(side=tk.LEFT, padx=5)
        
        # 注册按钮
        self.register_button = ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["register"],
            command=self.show_register_dialog,
            width=15
        )
        self.register_button.pack(side=tk.LEFT, padx=5)
        
        # 添加用户授权按钮（初始状态为禁用）
        self.auth_button = ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["user_auth"],
            command=self.show_auth_dialog,
            width=15,
            state=tk.DISABLED
        )
        self.auth_button.pack(side=tk.LEFT, padx=5)
        
        # 退出按钮
        self.quit_button = ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["quit"],
            command=self.root.quit,
            width=15
        )
        self.quit_button.pack(side=tk.LEFT, padx=5)
        
        # 加载保存的登录信息
        self.load_login_info()
        
        # 绑定回车键
        self.password_entry.bind('<Return>', lambda e: self.login())
        
        # 添加用户名存储变量
        self.current_username = None
        self.login_success = False

    def show_register_dialog(self):
        """显示注册对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(LANGUAGES["中文"]["register"])
        dialog.geometry("300x200")
        
        # 用户名输入
        ttk.Label(dialog, text=LANGUAGES["中文"]["username"]).pack(pady=5)
        username_entry = ttk.Entry(dialog)
        username_entry.pack(pady=5)
        
        # 密码输入
        ttk.Label(dialog, text=LANGUAGES["中文"]["password"]).pack(pady=5)
        password_entry = ttk.Entry(dialog, show="*")
        password_entry.pack(pady=5)
        
        def do_register():
            username = username_entry.get()
            password = password_entry.get()
            
            if not username or not password:
                messagebox.showerror(
                    LANGUAGES["中文"]["error"],
                    LANGUAGES["中文"]["empty_credentials"]
                )
                return
            
            success, message = self.user_manager.register_user(username, password)
            if success:
                messagebox.showinfo(LANGUAGES["中文"]["success"], message)
                dialog.destroy()
            else:
                messagebox.showerror(LANGUAGES["中文"]["error"], message)
        
        # 注册按钮
        ttk.Button(
            dialog,
            text=LANGUAGES["中文"]["register"],
            command=do_register
        ).pack(pady=20)

    def verify_credentials(self, username, password):
        """验证用户名和密码"""
        # root用户在login方法中直接验证，这里只处理普通用户
        if self.user_manager.verify_password(username, password):
            user = self.user_manager.get_user(username)
            if not user["is_active"]:
                messagebox.showerror(
                    LANGUAGES["中文"]["login_failed"],
                    LANGUAGES["中文"]["account_not_activated"]
                )
                return False
            return True
        return False

    def login(self):
        """登录验证"""
        username = self.username_entry.get()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror(
                LANGUAGES["中文"]["error"],
                LANGUAGES["中文"]["empty_credentials"]
            )
            return
        
        # root用户验证
        if username == "root" and password == "toor":
            self.current_username = username  # 保存当前用户名
            self.login_success = True  # 设置登录成功状态
            messagebox.showinfo(
                LANGUAGES["中文"]["success"],
                LANGUAGES["中文"]["admin_login_success"]
            )
            self.root.destroy()  # 直接销毁窗口
            return
        
        # 其他用户的验证
        if self.verify_credentials(username, password):
            user = self.user_manager.get_user(username)
            if not user["is_active"]:
                messagebox.showerror(
                    LANGUAGES["中文"]["login_failed"],
                    LANGUAGES["中文"]["account_not_activated"]
                )
                return
                
            self.save_login_info()
            self.current_username = username  # 保存当前用户名
            self.login_success = True
            messagebox.showinfo(
                LANGUAGES["中文"]["success"],
                LANGUAGES["中文"]["login_success"]
            )
            self.root.destroy()  # 直接销毁窗口
        else:
            messagebox.showerror(
                LANGUAGES["中文"]["login_failed"],
                LANGUAGES["中文"]["invalid_credentials"]
            )

    def load_login_info(self):
        """加载保存的登录信息"""
        try:
            if os.path.exists("login_info.json"):
                with open("login_info.json", "r") as f:
                    data = json.load(f)
                    if data.get("remember", False):
                        self.username_entry.insert(0, data.get("username", ""))
                        self.password_entry.insert(0, data.get("password", ""))
                        self.remember_var.set(True)
        except Exception as e:
            logger.error(f"加载登录信息失败: {e}")

    def save_login_info(self):
        """保存登录信息"""
        if self.remember_var.get():
            data = {
                "username": self.username_entry.get(),
                "password": self.password_entry.get(),
                "remember": True
            }
        else:
            data = {"remember": False}
        
        try:
            with open("login_info.json", "w") as f:
                json.dump(data, f)
        except Exception as e:
            logger.error(f"保存登录信息失: {e}")

    def show_auth_dialog(self):
        """显示用户授权对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(LANGUAGES["中文"]["user_auth"])
        dialog.geometry("500x400")
        
        # 创建用户列表框架
        list_frame = ttk.LabelFrame(dialog, text=LANGUAGES["中文"]["user_list"], padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建用户列表
        columns = ("username", "status", "permissions", "created_at")
        user_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # 设置列标题
        user_tree.heading("username", text=LANGUAGES["中文"]["username"])
        user_tree.heading("status", text=LANGUAGES["中文"]["status"])
        user_tree.heading("permissions", text=LANGUAGES["中文"]["permissions"])
        user_tree.heading("created_at", text=LANGUAGES["中文"]["created_at"])
        
        # 设置列宽
        user_tree.column("username", width=100)
        user_tree.column("status", width=80)
        user_tree.column("permissions", width=150)
        user_tree.column("created_at", width=150)
        
        user_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=user_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        user_tree.configure(yscrollcommand=scrollbar.set)
        
        def load_users():
            """加载用户列表"""
            user_tree.delete(*user_tree.get_children())
            for username, user_data in self.user_manager.users.items():
                if username != "root":  # 不显示root用户
                    status = LANGUAGES["中文"]["activated"] if user_data["is_active"] else LANGUAGES["中文"]["not_activated"]
                    permissions = ", ".join(user_data.get("permissions", []))
                    user_tree.insert("", tk.END, values=(
                        username,
                        status,
                        permissions,
                        user_data["created_at"]
                    ))
        
        def activate_selected_user():
            """激活选中的用户"""
            selected = user_tree.selection()
            if not selected:
                messagebox.showwarning(
                    LANGUAGES["中文"]["warning"],
                    LANGUAGES["中文"]["no_user_selected"]
                )
                return
            
            # 弹出密码验证对话框
            pwd_dialog = tk.Toplevel(dialog)
            pwd_dialog.title("管理员验证")
            pwd_dialog.geometry("300x150")
            
            ttk.Label(pwd_dialog, text="请输入管理员密码:").pack(pady=10)
            pwd_entry = ttk.Entry(pwd_dialog, show="*")
            pwd_entry.pack(pady=5)
            
            def do_activate():
                admin_pwd = pwd_entry.get()
                username = user_tree.item(selected[0])["values"][0]
                
                success, message = self.user_manager.activate_user(
                    "root",
                    admin_pwd,
                    username
                )
                
                if success:
                    messagebox.showinfo(LANGUAGES["中文"]["success"], message)
                    pwd_dialog.destroy()
                    load_users()  # 刷新用户列表
                else:
                    messagebox.showerror(LANGUAGES["中文"]["error"], message)
            
            ttk.Button(pwd_dialog, text="确认", command=do_activate).pack(pady=10)
            ttk.Button(pwd_dialog, text="取消", command=pwd_dialog.destroy).pack(pady=5)
            
            # 设置模态对话框
            pwd_dialog.transient(dialog)
            pwd_dialog.grab_set()
        
        def update_permissions():
            """更新用户权限"""
            selected = user_tree.selection()
            if not selected:
                messagebox.showwarning(
                    LANGUAGES["中文"]["warning"],
                    LANGUAGES["中文"]["no_user_selected"]
                )
                return
            
            # 弹出密码验证对话框
            pwd_dialog = tk.Toplevel(dialog)
            pwd_dialog.title("管理员验证")
            pwd_dialog.geometry("300x150")
            
            ttk.Label(pwd_dialog, text="请输入管理员密码:").pack(pady=10)
            pwd_entry = ttk.Entry(pwd_dialog, show="*")
            pwd_entry.pack(pady=5)
            
            def do_update():
                admin_pwd = pwd_entry.get()
                username = user_tree.item(selected[0])["values"][0]
                new_permissions = [
                    perm for perm, var in permission_vars.items()
                    if var.get()
                ]
                
                success, message = self.user_manager.update_user_permissions(
                    "root",
                    admin_pwd,
                    username,
                    new_permissions
                )
                
                if success:
                    messagebox.showinfo(LANGUAGES["中文"]["success"], message)
                    pwd_dialog.destroy()
                    load_users()  # 刷新用户列表
                else:
                    messagebox.showerror(LANGUAGES["中文"]["error"], message)
            
            ttk.Button(pwd_dialog, text="确认", command=do_update).pack(pady=10)
            ttk.Button(pwd_dialog, text="取消", command=pwd_dialog.destroy).pack(pady=5)
            
            # 设置模态对话框
            pwd_dialog.transient(dialog)
            pwd_dialog.grab_set()
        
        # 权限管理框架
        perm_frame = ttk.LabelFrame(dialog, text=LANGUAGES["中文"]["permission_management"], padding="10")
        perm_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 权限选择
        available_permissions = ["download", "upload", "edit", "delete"]  # 可用的权限列表
        permission_vars = {perm: tk.BooleanVar() for perm in available_permissions}
        
        for perm in available_permissions:
            ttk.Checkbutton(
                perm_frame,
                text=LANGUAGES["中文"][f"perm_{perm}"],
                variable=permission_vars[perm]
            ).pack(side=tk.LEFT, padx=5)
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 添加激活按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["activate_user"],
            command=activate_selected_user
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加权限更新按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["update_permissions"],
            command=update_permissions
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加刷新按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["refresh"],
            command=load_users
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加关闭按钮
        ttk.Button(
            button_frame,
            text=LANGUAGES["中文"]["close"],
            command=dialog.destroy
        ).pack(side=tk.RIGHT, padx=5)
        
        # 初始加载用户列表
        load_users()

# 修改主函数
def main():
    # 先显示登录窗口
    login_root = tk.Tk()
    login_window = LoginWindow(login_root)
    login_root.mainloop()
    
    # 如果登录成功，显示主窗口
    if login_window.login_success:
        root = ThemedTk(theme="clam")
        app = PDFDownloaderGUI(root, login_window.current_username)
        root.mainloop()

if __name__ == "__main__":
    main()
