#!/bin/bash
###
 # @Author: hgxszhj <EMAIL>
 # @Date: 2025-04-17 13:58:20
 # @LastEditors: hgxszhj <EMAIL>
 # @LastEditTime: 2025-04-17 14:45:18
 # @FilePath: /ai-deepseek-ce/setup.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

set -e

# 定义日志文件
LOG_FILE="setup_error.log"
# 启动时清空日志
> "$LOG_FILE"

# 彩色输出函数
echo_green() { echo -e "\033[32m$1\033[0m"; }
echo_red()   { echo -e "\033[31m$1\033[0m"; }
echo_yellow() { echo -e "\033[33m$1\033[0m"; }

# 检查 Node.js
if ! command -v node &>/dev/null; then
  echo_red "未检测到 Node.js，请先安装 Node.js 16 及以上版本。"
  exit 1
fi

# 检查 npm
if ! command -v npm &>/dev/null; then
  echo_red "未检测到 npm，请先安装 npm。"
  exit 1
fi

# 错误处理函数
fail_exit() {
  if [ $? -ne 0 ]; then
    echo_red "[错误] $1"
    echo "[错误] $1" >> "$LOG_FILE"
    echo "--- 相关日志 ---" >> "$LOG_FILE"
    tail -n 50 "$LOG_FILE"
    exit 1
  fi
}

exec 2>>$LOG_FILE

echo_green "=============================="
echo_green "1. 安装后端依赖..."
cd backend
if command -v conda &>/dev/null; then
  echo_green "检测到 conda，优先使用 conda 环境。"
  if conda env list | grep -q "deepseek-env"; then
    echo_yellow "conda 环境 deepseek-env 已存在，跳过创建。"
  else
    conda create -y -n deepseek-env python=3.9.11
    fail_exit "conda 环境创建失败，请检查conda配置或网络。"
  fi
  source "$(conda info --base)/etc/profile.d/conda.sh"
  conda activate deepseek-env
else
  echo_yellow "未检测到 conda，使用 Python venv 虚拟环境。"
  if ! command -v python3 &>/dev/null && ! command -v python &>/dev/null; then
    echo_red "未检测到 Python，请先安装 Python 3.8 及以上版本。"
    exit 1
  fi
  if [ ! -d "venv" ]; then
    python3 -m venv venv || python -m venv venv
    fail_exit "venv 创建失败，请检查 Python 安装。"
  else
    echo_yellow "venv 虚拟环境已存在，跳过创建。"
  fi
  source venv/bin/activate
fi

pip install --upgrade pip 2>&1 | tee -a "$LOG_FILE"
fail_exit "pip 升级失败，请检查网络或 PyPI 源。"
pip install -r requirements.txt 2>&1 | tee -a "$LOG_FILE"
fail_exit "后端依赖安装失败，请检查 requirements.txt 或网络。"
cd ..

echo_green "=============================="
echo_green "2. 安装前端依赖..."
cd frontend
#if [ -d "node_modules" ]; then
#  echo_yellow "node_modules 已存在，跳过 npm install。"
#else
#  npm install 2>&1 | tee -a "$LOG_FILE"
#  fail_exit "npm install 失败，请检查 Node.js/npm 配置或网络。"
#fi
if [ ! -d "node_modules" ]; then
  echo_yellow "未检测到 node_modules，后续启动时会自动 npm install。"
else
  echo_yellow "node_modules 已存在，后续启动无需重复 npm install。"
fi
cd ..

echo_green "=============================="
echo_green "3. 构建前端..."
cd frontend
npm run build 2>&1 | tee -a "$LOG_FILE"
fail_exit "前端构建失败，请检查前端代码或依赖。"
cd ..

echo_green "\n全部依赖安装和前端构建完成！"
echo_yellow "\n你可以用如下命令启动服务："
echo_yellow "  ./start.sh    # 一键启动前后端（推荐）"
echo_yellow "  或分别手动："
echo_yellow "    cd backend && source venv/bin/activate 或 conda activate deepseek-env && python main.py"
echo_yellow "    cd frontend && npm start (需重新 npm install)"
echo_green "\n前端生产构建目录：frontend/build，可用 nginx/http-server 等托管。"
echo_yellow "\n如需瘦身可手动删除 frontend/node_modules。"
echo_yellow "\n如遇常见问题请参考 README.md 或联系维护者。" 