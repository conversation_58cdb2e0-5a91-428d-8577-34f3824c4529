.app-layout {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.app-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background-color: #f0f2f5;
  background-image: linear-gradient(to bottom right, rgba(240, 242, 245, 0.8), rgba(245, 247, 250, 0.8));
}

/* 消息卡片动画和悬停效果 */
.message-card {
  transition: all 0.3s ease;
}

.message-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.message-card:hover .message-actions {
  opacity: 1 !important;
}

/* 美化滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 消息容器样式 */
.message-container {
  position: relative;
}

/* Markdown 内容样式 */
.markdown-content {
  font-size: 15px;
  line-height: 1.6;
}

.markdown-content pre {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #e8e8e8;
  padding: 8px 12px;
}

.markdown-content table th {
  background-color: #fafafa;
}

.markdown-content a {
  color: #1890ff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content img {
  max-width: 100%;
  margin: 16px 0;
  border-radius: 8px;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message-card, .ai-message-card {
  animation: fadeIn 0.3s ease-out forwards;
}

/* 标题样式 */
.ant-typography {
  line-height: 1.5;
}

/* 加载动画 */
.ant-spin {
  transition: all 0.3s ease;
}

/* 卡片样式 */
.ant-card {
  border-radius: 12px;
  overflow: hidden;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.ant-card:hover {
  transform: translateY(-1px);
}

/* 按钮样式增强 */
.ant-btn {
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 模型选择下拉框样式 */
.ant-select:hover .ant-select-selector {
  border-color: #40a9ff !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-content {
    padding: 16px 12px;
  }
  
  .message-card {
    max-width: 90% !important;
  }
  
  .ant-form-item {
    margin-bottom: 12px !important;
  }
}

/* 主题颜色 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --heading-color: #262626;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --disabled-color: #bfbfbf;
  --border-color: #d9d9d9;
  --background-color: #f0f2f5;
}