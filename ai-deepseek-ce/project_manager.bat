@echo off
chcp 65001 >nul
:menu
echo.
echo ======== AI-DeepSeek 项目管理脚本 (Windows) ========
echo 1. 安装依赖和构建（setup）
echo 2. 启动服务（start）
echo 3. 项目瘦身（移除 frontend\node_modules 和 backend\venv）
echo 4. 退出
set /p choice=请输入选项 [1-4]:

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto start
if "%choice%"=="3" goto slim
if "%choice%"=="4" exit
echo 选项无效，请重新输入。
goto menu

:setup
echo [1] 安装后端依赖...
cd backend
if exist venv (
    echo venv 已存在，跳过创建。
) else (
    python -m venv venv
)
call venv\Scripts\activate
pip install --upgrade pip
pip install -r requirements.txt
cd ..
echo [2] 检查前端依赖...
cd frontend
if exist node_modules (
    echo node_modules 已存在，跳过 npm install。
) else (
    npm install
)
echo [3] 构建前端...
npm run build
cd ..
echo 全部依赖安装和前端构建完成！
pause
goto menu

:start
echo 启动后端服务...
cd backend
start "" cmd /c "call venv\Scripts\activate && python main.py > ..\backend.log"
cd ..
echo 启动前端服务...
cd frontend
if not exist node_modules (
    echo 未检测到 node_modules，自动执行 npm install...
    npm install
)
start "" npm start
cd ..
echo 所有服务已启动！
pause
goto menu

:slim
echo 请选择瘦身操作：
echo 1. 仅移除 frontend\node_modules
echo 2. 同时移除 frontend\node_modules 和 backend\venv
echo 3. 取消
set /p slimchoice=请输入选项 [1-3]:
if "%slimchoice%"=="1" (
    if exist frontend\node_modules (
        rmdir /s /q frontend\node_modules
        echo 已移除 frontend\node_modules
    ) else (
        echo frontend\node_modules 不存在，无需删除。
    )
)
if "%slimchoice%"=="2" (
    if exist frontend\node_modules (
        rmdir /s /q frontend\node_modules
        echo 已移除 frontend\node_modules
    ) else (
        echo frontend\node_modules 不存在，无需删除。
    )
    if exist backend\venv (
        rmdir /s /q backend\venv
        echo 已移除 backend\venv
    ) else (
        echo backend\venv 不存在，无需删除。
    )
)
echo 操作完成。
pause
goto menu