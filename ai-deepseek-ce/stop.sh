#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${GREEN}======== AI-DeepSeek 停止脚本 ========${NC}"

# 停止后端
if [ -f backend.pid ]; then
  BACKEND_PID=$(cat backend.pid)
  if ps -p $BACKEND_PID > /dev/null; then
    echo -e "${YELLOW}停止后端服务 (PID: $BACKEND_PID)...${NC}"
    kill -9 $BACKEND_PID 2>/dev/null
    echo -e "${GREEN}后端服务已停止!${NC}"
  else
    echo -e "${YELLOW}后端服务已经不在运行了 (PID: $BACKEND_PID)${NC}"
  fi
  rm backend.pid
else
  echo -e "${YELLOW}未找到后端PID文件，尝试通过端口查找进程...${NC}"
  BACKEND_PIDS=$(lsof -t -i:8000 2>/dev/null)
  if [ -n "$BACKEND_PIDS" ]; then
    echo -e "${YELLOW}找到后端进程: $BACKEND_PIDS${NC}"
    kill -9 $BACKEND_PIDS 2>/dev/null
    echo -e "${GREEN}已停止后端进程!${NC}"
  else
    echo -e "${YELLOW}未发现后端进程在运行${NC}"
  fi
fi

# 停止前端
if [ -f frontend.pid ]; then
  FRONTEND_PID=$(cat frontend.pid)
  if ps -p $FRONTEND_PID > /dev/null; then
    echo -e "${YELLOW}停止前端服务 (PID: $FRONTEND_PID)...${NC}"
    kill -9 $FRONTEND_PID 2>/dev/null
    echo -e "${GREEN}前端服务已停止!${NC}"
  else
    echo -e "${YELLOW}前端服务已经不在运行了 (PID: $FRONTEND_PID)${NC}"
  fi
  rm frontend.pid
else
  echo -e "${YELLOW}未找到前端PID文件，尝试通过端口查找进程...${NC}"
  FRONTEND_PIDS=$(lsof -t -i:3000 2>/dev/null)
  if [ -n "$FRONTEND_PIDS" ]; then
    echo -e "${YELLOW}找到前端进程: $FRONTEND_PIDS${NC}"
    kill -9 $FRONTEND_PIDS 2>/dev/null
    echo -e "${GREEN}已停止前端进程!${NC}"
  else
    echo -e "${YELLOW}未发现前端进程在运行${NC}"
  fi
fi

# 确保没有残留进程
echo -e "${YELLOW}检查是否有残留进程...${NC}"
BACKEND_CHECK=$(lsof -t -i:8000 2>/dev/null)
FRONTEND_CHECK=$(lsof -t -i:3000 2>/dev/null)

if [ -n "$BACKEND_CHECK" ]; then
  echo -e "${YELLOW}发现后端残留进程: $BACKEND_CHECK，正在停止...${NC}"
  kill -9 $BACKEND_CHECK 2>/dev/null
fi

if [ -n "$FRONTEND_CHECK" ]; then
  echo -e "${YELLOW}发现前端残留进程: $FRONTEND_CHECK，正在停止...${NC}"
  kill -9 $FRONTEND_CHECK 2>/dev/null
fi

echo -e "${GREEN}======== 所有服务已停止 ========${NC}" 